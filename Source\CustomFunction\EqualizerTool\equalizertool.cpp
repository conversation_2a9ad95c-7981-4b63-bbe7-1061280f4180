#include <QDebug>
#include <QFileDialog>

#include "tkspline.h"
#include "equalizertool.h"


EqualizerTool::EqualizerTool(unsigned int freqStart, unsigned int freqEnd, unsigned int sampleCount, bool check)
    : mFreqStart(freqStart)
    , mFreqEnd(freqEnd)
{
    double coefficients=qLn(freqEnd / freqStart) / sampleCount;
    int valuePre=0, valueNew=0;
    for(unsigned int i=0;i<sampleCount;i++)
    {
        valueNew = freqStart * qExp(coefficients * i);
        if(valuePre != valueNew)
        {
            valuePre = valueNew;
            mSampleTick.append(valuePre);
        }
    }
    mCurveSource.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveTarget.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveCombined.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveFilteredRaw.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveFilteredRawAlignment.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveFilteredCompensated.resize(mSampleTick.count(), QPointF(0, 0));
    mCurveFilteredCompensatedAlignment.resize(mSampleTick.count(), QPointF(0, 0));
    if(check) qWarning() << "EqualizerTool::EqualizerTool //SampleCount Request[" << sampleCount << "] Actual[" << mSampleTick.count() << "]";
}
EqualizerTool::~EqualizerTool()
{
    for(auto element : mFrequencyResponse)
    {
        delete element;
    }
    for(auto element : mCurveBand)
    {
        delete element;
    }
}


// setter & getter
QString EqualizerTool::importCurve(QString name, QString path)
{
    QHash<QString, QVector<QPointF>*> supported;
    supported["Source"] = &mCurveSource;
    supported["Target"] = &mCurveTarget;
    if(supported.contains(name))
    {
        if(path.isEmpty())
        {
            QString notice="Select FR File";
            if(mLanguage == "English") notice = "Select FR File";
            else if(mLanguage == "Chinese") notice = "选择频响文件";
            path = QFileDialog::getOpenFileName(nullptr, notice, "", "FR Files (*.csv *.txt *.mic)");
        }
        if(path.isEmpty())
        {
            return "";
        }
        // return loadCurve(path, *(supported.value(name)));
        bool ret = loadCurve(path, *(supported.value(name)));
        calculateCurveFiltered();
        return ret ? (path) : ("");
    }
    qWarning() << "EqualizerTool::importCurve //The current curve does not support import";
    return "";
}
QString EqualizerTool::exportCurve(QString name, QString path)
{
    QHash<QString, QVector<QPointF>*> supported;
    supported["Combined"] = &mCurveCombined;
    if(supported.contains(name))
    {
        if(path.isEmpty())
        {
            QString notice="Save FR File";
            if(mLanguage == "English") notice = "Save FR File";
            else if(mLanguage == "Chinese") notice = "保存频响文件";
            path = QFileDialog::getSaveFileName(nullptr, notice, "", "FR Files (*.csv)");
        }
        if(path.isEmpty())
        {
            return "";
        }
        QFile file(path);
        if(!file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            return "";
        }
        QTextStream streamOut(&file);
        streamOut << "fre(Hz),raw(dB)\n";
        for(unsigned int i=0;i<mSampleTick.count();i++)
        {
            streamOut << QString::number(supported.value(name)->at(i).x()) << "," << QString::number(supported.value(name)->at(i).y()) << "\n";
        }
        file.close();
        return path;
    }
    qWarning() << "EqualizerTool::exportCurve //The current curve does not support export";
    return "";
}
QString EqualizerTool::importBandData(QString path)
{
    if(path.isEmpty())
    {
        QString notice="Import Filter Parameters";
        if(mLanguage == "English") notice = "Import Filter Parameters";
        else if(mLanguage == "Chinese") notice = "导入滤波参数";
        path = QFileDialog::getOpenFileName(nullptr, notice, "", "txt Files (*.txt)");
    }
    if(path.isEmpty())
    {
        return "";
    }
    QFile file(path);
    if(!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << "EqualizerTool::importBandData //failed to open filter parameters file";
        return "";
    }
    QTextStream streamIn(&file);
    QString title;
    bool isValid=false;
    while(!streamIn.atEnd())
    {
        title = streamIn.readLine();
        if(title == "EQFilterParameters")
        {
            isValid = true;
            break;
        }
    }
    if(isValid)
    {
        QStringList item;
        BandData data;
        QVector<BandData> dataList;
        while(!streamIn.atEnd())
        {
            item = streamIn.readLine().split(",");
            if(item.count() != 6)
            {
                continue;
            }
            data.name = item[0];
            data.status = item[1].toInt();
            data.type = (FilterType) item[2].toInt();
            data.freq = item[3].toDouble();
            data.gain = item[4].toDouble();
            data.q = item[5].toDouble();
            dataList.append(data);
        }
        file.close();
        modifyBandData(dataList);
        return path;
    }
    return "";
}
QString EqualizerTool::exportBandData(QString path)
{
    if(path.isEmpty())
    {
        QString notice="Export Filter Parameters";
        if(mLanguage == "English") notice = "Export Filter Parameters";
        else if(mLanguage == "Chinese") notice = "导出滤波参数";
        path = QFileDialog::getSaveFileName(nullptr, notice, "", "txt Files (*.txt)");
    }
    if(path.isEmpty())
    {
        return "";
    }
    QFile file(path);
    if(!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        return "";
    }
    QTextStream streamOut(&file);
    streamOut << "EQFilterParameters\n";
    for(auto element : mFilterParameter)
    {
        streamOut << element.name << ",";
        streamOut << QString::number(element.status) << ",";
        streamOut << QString::number(element.type) << ",";
        streamOut << QString::number(element.freq) << ",";
        streamOut << QString::number(element.gain) << ",";
        streamOut << QString::number(element.q) << "\n";
    }
    file.close();
    return path;
}
void EqualizerTool::modifyBandData(QString name, bool status, FilterType type, double freq, double gain, double q, double sampleRate)
{
    BandData data;
    data.name = name;
    data.status = status;
    data.type = type;
    data.freq = freq;
    data.gain = gain;
    data.q = q;
    mFilterParameter[name] = data;
    if(!mFrequencyResponse.contains(name))
    {
        mFrequencyResponse[name] = new QVector<std::complex<double>>;
        mCurveBand[name] = new QVector<QPointF>();
    }
    if(status)
    {
        mFilterCoefficient[name] = calculateFilterCoefficient(type, freq, gain, q, sampleRate);
    }
    else
    {
        mFilterCoefficient[name] = calculateFilterCoefficient(Peaking, 3000, 10, 12, sampleRate);
    }
    calculateCurve(name);
    calculateCurveCombined();
    calculateCurveFiltered();
}
void EqualizerTool::modifyBandData(BandData data, double sampleRate)
{
    modifyBandData(data.name, data.status, data.type, data.freq, data.gain, data.q, sampleRate);
}
void EqualizerTool::modifyBandData(QVector<BandData> dataList, double sampleRate)
{
    if(dataList.count() > 1)
    {
        for(auto element : mFrequencyResponse)
        {
            delete element;
        }
        for(auto element : mCurveBand)
        {
            delete element;
        }
        mFilterParameter.clear();
        mFilterCoefficient.clear();
        mFrequencyResponse.clear();
        mCurveBand.clear();
    }
    for(auto element : dataList)
    {
        mFilterParameter[element.name] = element;
        if(!mFrequencyResponse.contains(element.name))
        {
            mFrequencyResponse[element.name] = new QVector<std::complex<double>>;
            mCurveBand[element.name] = new QVector<QPointF>();
        }
        if(element.status)
        {
            mFilterCoefficient[element.name] = calculateFilterCoefficient(element.type, element.freq, element.gain, element.q, sampleRate);
        }
        else
        {
            mFilterCoefficient[element.name] = calculateFilterCoefficient(Peaking, mFreqStart, 0, 0.7070, sampleRate);
        }
        calculateCurve(element.name);
    }
    calculateCurveCombined();
    calculateCurveFiltered();
}
QVector<QPointF>& EqualizerTool::getCurve(QString name)
{
    if(name == "Source") return mCurveSource;
    else if(name == "Target") return mCurveTarget;
    else if(name == "Combined") return mCurveCombined;
    else if(name == "FilteredRaw") return mCurveFilteredRaw;
    else if(name == "FilteredRawAlignment") return mCurveFilteredRawAlignment;
    else if(name == "FilteredCompensated") return mCurveFilteredCompensated;
    else if(name == "FilteredCompensatedAlignment") return mCurveFilteredCompensatedAlignment;
    else return *(mCurveBand.value(name));
}


EqualizerTool::FilterCoefficient EqualizerTool::calculateFilterCoefficient(FilterType type, double freq, double gain, double q, double sampleRate)
{
    Q_UNUSED(sampleRate);
    FilterCoefficient filterCoefficients;
    filterCoefficients.numerator = {0, 0, 0};
    filterCoefficients.denominator = {1, 1, 1};
    double w0=2 * mPI * freq;
    double linearGainSqrt=sqrt(pow(10, gain / 20));
    switch(type)
    {
        case Peaking:
            filterCoefficients.numerator = {1, linearGainSqrt * (1 / q) * w0, pow(w0, 2)};
            filterCoefficients.denominator = {1, ((1 / q) / linearGainSqrt) * w0, pow(w0, 2)};
            break;
        case LowShelf:
            filterCoefficients.numerator = {1, (w0 * sqrt(linearGainSqrt)) / q, linearGainSqrt * pow(w0, 2)};
            filterCoefficients.denominator = {1, w0 / (q * sqrt(linearGainSqrt)), 1 / linearGainSqrt * pow(w0, 2)};
            break;
        case HighShelf:
            filterCoefficients.numerator = {linearGainSqrt, (w0 * sqrt(linearGainSqrt)) / q, pow(w0, 2)};
            filterCoefficients.denominator = {1 / linearGainSqrt, w0 / (q * sqrt(linearGainSqrt)), pow(w0, 2)};
            break;
        case LowPass:
            filterCoefficients.numerator = {0, 0, pow(w0, 2)};
            filterCoefficients.denominator = {1, (1 / q) * w0, pow(w0, 2)};
            break;
        case HighPass:
            filterCoefficients.numerator = {1, 0, 0};
            filterCoefficients.denominator = {1, w0 / q, pow(w0, 2)};
            break;
        case BandPass:
            break;
        case BandStop:
            break;
        case Notch:
            break;
        case AllPass:
            break;
        case Custom:
            break;
        default:
            break;
    }
    return filterCoefficients;
}
void EqualizerTool::calculateCurve(QString name)
{
    QVector<double> numerator, denominator;
    mFrequencyResponse.value(name)->clear();
    mCurveBand.value(name)->clear();
    for(unsigned int i=0;i<mSampleTick.count();i++)
    {
        std::complex<double> jw(0, 2 * mPI * mSampleTick[i]);
        numerator = mFilterCoefficient.value(name).numerator;
        denominator = mFilterCoefficient.value(name).denominator;
        std::complex<double> value=(numerator[0] * pow(jw, 2) + numerator[1] * jw + numerator[2]) / (denominator[0] * pow(jw, 2) + denominator[1] * jw + denominator[2]);
        mFrequencyResponse.value(name)->append(value);
        mCurveBand.value(name)->append(QPointF(mSampleTick[i], 20 * std::log10(std::abs(value))));
    }
}
void EqualizerTool::calculateCurveCombined()
{
    mCurveCombined.clear();
    for(unsigned int i=0;i<mSampleTick.count();i++)
    {
        std::complex<double> value=1;
        for(auto element : mFrequencyResponse)
        {
            value *= element->at(i);
        }
        mCurveCombined.append(QPointF(mSampleTick[i], 20 * std::log10(std::abs(value))));
    }
}
void EqualizerTool::calculateCurveFiltered()
{
    double filtered=0, pointAlignment=mSampleTick.count() / 2;
    mCurveFilteredRaw.clear();
    mCurveFilteredRawAlignment.clear();
    mCurveFilteredCompensated.clear();
    mCurveFilteredCompensatedAlignment.clear();
    for(unsigned int i=0;i<mSampleTick.count();i++)
    {
        filtered = mCurveSource[i].y() + mCurveCombined[i].y();
        mCurveFilteredRaw.append(QPointF(mSampleTick[i], filtered));
        mCurveFilteredRawAlignment.append(QPointF(mSampleTick[i], filtered - mCurveSource[pointAlignment].y() - mCurveCombined[pointAlignment].y()));
        mCurveFilteredCompensated.append(QPointF(mSampleTick[i], filtered - mCurveTarget[i].y()));
        mCurveFilteredCompensatedAlignment.append(QPointF(mSampleTick[i], filtered - mCurveSource[pointAlignment].y() - mCurveCombined[pointAlignment].y() - mCurveTarget[i].y()));
    }
}
bool EqualizerTool::loadCurve(QString path, QVector<QPointF>& curve)
{
    QFile file(path);
    if(!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << "EqualizerTool::loadCurve //failed to open curve file";
        return false;
    }
    QTextStream streamIn(&file);
    QStringList titleList;
    bool isValid=false;
    while(!streamIn.atEnd())
    {
        titleList = streamIn.readLine().split(",");
        if(titleList[0].toLower().contains("fre(hz)") && titleList[1].toLower().contains("raw(db)"))
        {
            isValid = true;
            break;
        }
    }
    if(isValid)
    {
        QStringList item;
        std::vector<double> fre, raw;
        while(!streamIn.atEnd())
        {
            item = streamIn.readLine().split(",");
            fre.push_back(item[0].toDouble());
            raw.push_back(item[1].toDouble());
        }
        file.close();
        tk::spline tkSpline;
        tkSpline.set_points(fre, raw);
        curve.clear();
        for(unsigned int i=0;i<mSampleTick.count();i++)
        {
            curve.append(QPointF(mSampleTick[i], tkSpline(mSampleTick[i])));
        }
        return true;
    }
    file.close();
    return false;
}

