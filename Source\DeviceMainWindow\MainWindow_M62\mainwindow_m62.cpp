#include <QList>
#include <QFileDialog>
#include <qnamespace.h>
#include <qscrollarea.h>

#include "workspace.h"
#include "globalfont.h"
#include "usbaudioapi.h"
#include "appsettings.h"
#include "updaterfactory.h"
#include "mainwindow_m62.h"
#include "ui_mainwindow_m62.h"


MainWindow_M62::MainWindow_M62(QWidget* parent)
    : MainWindow_Base(parent)
    , ui(new Ui::MainWindow_M62)
{
    ui->setupUi(this);
    setStyleSheet("background-color: rgb(0, 0, 0);");
    QString style;
    style = "QStatusBar {"
            "   background-color: rgb(31, 31, 31);"
            "   color: white;"
            "}"
            "QSizeGrip {"
            "   image: none;"
            "   background-color: rgb(31, 31, 31);"
            "   width: 16px;"
            "   height: 16px;"
            "}";
    ui->statusBar->setStyleSheet(style);
    ui->statusBar->setFont(GLBFHandle.font());
    // 初始化界面
    setupMainWindowWidget();
    setupPageMble();
    setupPageLive();
    setupPageTyro();
    setupPageProf();
    if(!QSettings().contains("M62/PageID")) QSettings().setValue("M62/PageID", PageLive);
    // 连接信号
    connect(&mDevice, &DeviceM62::newFrameReceived, this, &MainWindow_M62::in_mDevice_newFrameReceived, Qt::UniqueConnection);
    // 指定设备
    assignDevice(&mDevice, "M62");
    // 指定固件升级方式
    UpdaterFactory::setMethod(UpdaterFactory::UpdaterMethod::Method1);
    // 查看帧率
    // showFrameRate();
    // 查看发送帧
    // showSendingFrame();
    // 查看控件输出
    // connect(&Widget, &WidgetType::attributeChanged, this, [this](QString objectName, QString attribute, QString value){ qDebug() << objectName << attribute << value; });
}
MainWindow_M62::~MainWindow_M62()
{
    delete ui;
}


// override
void MainWindow_M62::resizeEvent(QResizeEvent* e)
{
    int statusBarHeight=height() * 0.0248;
    QFont font=ui->statusBar->font();
    font.setPointSize(GLBFHandle.getSuitablePointSize(font, statusBarHeight) - 1);
    ui->statusBar->setFont(font);
    ui->statusBar->setFixedHeight(statusBarHeight);
    mPageLiveWidgetDucking.setWidthHeight(false);
    mSettings.setWidthHeight(false);
    int space=height() * 0.0047;
    // PageMble
    ui->gridLayout_13->setVerticalSpacing(space);
    ui->gridLayout_13->setContentsMargins(0, 0, 0, space);
    ui->gridLayout_16->setVerticalSpacing(space);
    ui->gridLayout_16->setContentsMargins(space, 0, space, 0);
    ui->PageMbleSplitterE_O->setHandleWidth(space);
    ui->gridLayout_15->setHorizontalSpacing(space);
    // PageLive
    ui->gridLayout->setVerticalSpacing(space);
    ui->gridLayout->setContentsMargins(0, 0, 0, space);
    ui->PageLiveSplitterIE_MLO->setHandleWidth(space);
    ui->gridLayout_2->setVerticalSpacing(space);
    ui->gridLayout_2->setContentsMargins(space, 0, 0, 0);
    ui->gridLayout_3->setVerticalSpacing(space);
    ui->gridLayout_3->setContentsMargins(0, 0, space, 0);
    ui->PageLiveSplitterL_O->setHandleWidth(space);
    ui->gridLayout_4->setHorizontalSpacing(space);
    // PageProf
    ui->gridLayout_11->setVerticalSpacing(space);
    ui->gridLayout_11->setContentsMargins(0, 0, 0, space);
    ui->PageProfSplitterIL_MO->setHandleWidth(space);
    ui->gridLayout_8->setVerticalSpacing(space);
    ui->gridLayout_8->setContentsMargins(space, 0, 0, 0);
    ui->gridLayout_9->setVerticalSpacing(space);
    ui->gridLayout_9->setContentsMargins(0, 0, space, 0);
    ui->gridLayout_10->setHorizontalSpacing(space);
    switch(mCurrentPage)
    {
        case PageLive:
            if(QSettings().contains("M62/PageLiveWidth")) QSettings().setValue("M62/PageLiveWidth", width());
            break;
        case PageTyro:
            break;
        case PageProf:
            if(QSettings().contains("M62/PageProfWidth")) QSettings().setValue("M62/PageProfWidth", width());
            break;
        default:
            break;
    }
}
void MainWindow_M62::closeEvent(QCloseEvent* e)
{
    e->ignore();
    hide();
    mSettings.hide();
    mPageLiveWidgetDucking.hide();
}
void MainWindow_M62::changeEvent(QEvent *event)
{
#ifdef Q_OS_MACOS
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized()) {
            if(mPageLiveWidgetDucking.isVisible()) {
                mPageLiveWidgetDucking.hide();
                mPageLiveWidgetDucking.setRestore(true);
            }
            if(mSettings.isVisible()) {
                mSettings.hide();
                mSettings.setRestore(true);
            }
        } else if (isVisible()) {
            mPageLiveWidgetDucking.restoreWindow();
            mSettings.restoreWindow();
        }
    }
#endif
    QWidget::changeEvent(event);
}
void MainWindow_M62::sendAuthInfoToDevice()
{
    mDevice.in_fieldSystem_attributeChanged("MainWindow", "Auth", "1");
}
void MainWindow_M62::onAuthResult(int result)
{
    if(result == -1) setPageToActive(PageFcty);
    else if(result == 1) setPageToActive(PageMble);
    else if(result == 2) setPageToActive((PageID) QSettings().value("M62/PageID").toInt());
    else if(result == 3) setPageToActive(PageProf);
}
void MainWindow_M62::onUSBAudioAttributeChanged(QString attribute, QString value)
{
    Q_UNUSED(value);
    if(attribute == "SampleRate")
    {
        QVector<unsigned int> supportedSampleRate=USBAHandle.getSampleRateOfActiveDeviceSupported();
        QVector<unsigned int> supportedBufferSize=USBAHandle.getBufferSizeOfActiveDeviceSupported();
        unsigned int sampleRate=USBAHandle.getSampleRateOfActiveDevice();
        unsigned int bufferSize=USBAHandle.getBufferSizeOfActiveDevice();
        ui->PageMbleFieldHead->modifySampleRateList(supportedSampleRate, sampleRate);
        ui->PageMbleFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        ui->PageLiveFieldHead->modifySampleRateList(supportedSampleRate, sampleRate);
        ui->PageLiveFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        ui->PageProfFieldHead->modifySampleRateList(supportedSampleRate, sampleRate);
        ui->PageProfFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        mSettingsAudio.modifySampleRateList(supportedSampleRate, sampleRate);
        mSettingsAudio.modifyBufferSizeList(supportedBufferSize, bufferSize);
    }
    else if(attribute == "BufferSize")
    {
        QVector<unsigned int> supportedBufferSize=USBAHandle.getBufferSizeOfActiveDeviceSupported();
        unsigned int bufferSize=USBAHandle.getBufferSizeOfActiveDevice();
        ui->PageMbleFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        ui->PageLiveFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        ui->PageProfFieldHead->modifyBufferSizeList(supportedBufferSize, bufferSize);
        mSettingsAudio.modifyBufferSizeList(supportedBufferSize, bufferSize);
    }
    else if(attribute == "SafeMode")
    {
        mSettingsAudio.setSafeMode(USBAHandle.getSafeModeOfActiveDevice());
    }
}


// slot
void MainWindow_M62::in_mDevice_newFrameReceived(DeviceType1::FrameInfo frame)
{
    switch(frame.cmd & 0xf000)
    {
        case DeviceM62::cmdN:
            break;
        case DeviceM62::cmdS:
            doNewFrameReceived_cmdS(frame);
            break;
        case DeviceM62::cmdI:
            doNewFrameReceived_cmdI(frame);
            break;
        case DeviceM62::cmdM:
            doNewFrameReceived_cmdM(frame);
            break;
        case DeviceM62::cmdE:
            doNewFrameReceived_cmdE(frame);
            break;
        case DeviceM62::cmdL:
            doNewFrameReceived_cmdL(frame);
            break;
        case DeviceM62::cmdO:
            doNewFrameReceived_cmdO(frame);
            break;
        case DeviceM62::cmdIEQ:
            doNewFrameReceived_cmdIEQ(frame);
            break;
        case DeviceM62::cmdOEQ:
            doNewFrameReceived_cmdOEQ(frame);
            break;
        case DeviceM62::cmdT:
            doNewFrameReceived_cmdT(frame);
            break;
        default:
            break;
    }
}
void MainWindow_M62::on_PageFctyPushButton1_clicked()
{
    emit attributeChanged("M62", "UpdateFirmware", "");
}
void MainWindow_M62::on_PageFctyPushButton2_clicked()
{
    QString notice="Select Firmware File";
    if(QSettings().value("Language").toString() == "English") notice = "Select Firmware File";
    else if(QSettings().value("Language").toString() == "Chinese") notice = "选择固件文件";
    QString file=QFileDialog::getOpenFileName(this, notice, "", "M62_FW Files (M62_FW*.bin M62_FW*.zip)");
    if(!file.isEmpty())
    {
        emit attributeChanged("M62", "UpdateFirmware", file);
    }
}


// init MainWindow
void MainWindow_M62::initMainWindowWidget()
{
    // Workspace
    connect(&WKSPHandle, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // Settings
    mSettings.setParent(this);
    mSettings.setResizable(false);
    mSettings.hide();
    mSettings.setName("Settings");
    mSettings.setFont(GLBFHandle.font());
    mSettings.registerLangage({
        {"Chinese", {
            {0, "系统设置"},
            {1, "设备设置"},
            {2, "ASIO设置"},
            {3, "关于"}
        }},
        {"English", {
            {0, "System"},
            {1, "Device"},
            {2, "ASIO"},
            {3, "About"}
        }}
    });
    mSettings.addTab(new ScrollArea<WidgetSytem1>(&mSettingsSystem, &mSettings), "System");
    mSettings.addTab(new ScrollArea<M62_PrivateWidget7>(&mSettingsDevice, &mSettings), "Device");
    mSettings.addTab(new ScrollArea<WidgetAudio1>(&mSettingsAudio, &mSettings), "ASIO");
    mSettings.addTab(new ScrollArea<WidgetAbout1>(&mSettingsAbout, &mSettings), "About");
    // SettingsSystem
    mSettingsSystem.setName("SettingsSystem");
    mSettingsSystem.setFont(GLBFHandle.font());
    mSettingsSystem.setLanguage(QSettings().value("Language").toString());
    mSettingsSystem.setInterfaceScale(QSettings().value("ScaleFactor").toFloat());
    mSettingsSystem.setFollowSystem(QSettings().value("FollowSystemScale").toBool());
    mSettingsSystem.setAutoStartByRegedit(QSettings().value("AutoStartOnBoot").toBool());
    mSettingsSystem.setAutoCheckUpdate(QSettings().value("AutoCheckForUpdates").toBool());
    mSettingsSystem.setAutoSaveWorkspace(QSettings().value("AutoSaveWorkspace").toBool());
    // SettingsAudio
    mSettingsAudio.setName("SettingsAudio");
    mSettingsAudio.setFont(GLBFHandle.font());
    // SettingsAbout
    mSettingsAbout.setName("SettingsAbout");
    mSettingsAbout.setFont(GLBFHandle.font());
    mSettingsAbout.setDeviceName("M62");
    // SettingsDevice
    mSettingsDevice.setName("SettingsDevice");
    mSettingsDevice.setFont(GLBFHandle.font());
    connect(&mSettingsDevice, &M62_PrivateWidget7::attributeChanged, &mDevice, &DeviceM62::in_Others_attributeChanged);
    // WidgetEQ_IN1
    mWidgetEQ_IN1.setParent(this);
    mWidgetEQ_IN1.hide();
    mWidgetEQ_IN1.setName("EQ_IN1");
    mWidgetEQ_IN1.setFont(GLBFHandle.font());
    mWidgetEQ_IN1.setEqualizerBands(4);
    connect(&mWidgetEQ_IN1, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Equalizer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // WidgetEQ_IN2
    mWidgetEQ_IN2.setParent(this);
    mWidgetEQ_IN2.hide();
    mWidgetEQ_IN2.setName("EQ_IN2");
    mWidgetEQ_IN2.setFont(GLBFHandle.font());
    mWidgetEQ_IN2.setEqualizerBands(4);
    connect(&mWidgetEQ_IN2, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Equalizer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // WidgetEQ_HP
    mWidgetEQ_HP.setParent(this);
    mWidgetEQ_HP.hide();
    mWidgetEQ_HP.setName("EQ_HP");
    mWidgetEQ_HP.setFont(GLBFHandle.font());
    mWidgetEQ_HP.setEqualizerBands(10);
    connect(&mWidgetEQ_HP, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Equalizer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // Linkage
    connect(&mSettingsAbout, &WidgetAbout1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        if(attribute == "UpdateFirmware")
        {
            mDevice.in_Others_attributeChanged(objectName, attribute, value);
            mDevice.stop();
            int counter=0;
            while(USBAHandle.isDeviceOnline())
            {
                counter++;
                if(counter >= 100)
                {
                    break;
                }
                QThread::msleep(10);
            };
            while(!USBAHandle.setDeviceToActiveByName("M62")) { };
            emit attributeChanged("M62", "UpdateFirmware", "");
        }
        else if(attribute == "UpdateSoftware")
        {
            emit attributeChanged("M62", "UpdateSoftware", "");
        }
    });
    connect(&mSettingsDevice, &M62_PrivateWidget7::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "DisplayMode")
        {
            if(value == "Easy mode")
            {
                setPageToActive(PageTyro);
                mSettings.hide();
            }
            else
            {
                mDevice.in_Others_attributeChanged(objectName, "FirmwareSelected", value);
            }
        }
    });
    connect(&mWidgetEQ_IN1, &EqualizerBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Switch")
        {
            mPageProfFieldInput_IN1.setValueEQ(value.toInt());
            mPageLiveFieldInput_IN1.setValueEQ(value.toInt());
        }
    });
    connect(&mWidgetEQ_IN2, &EqualizerBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Switch")
        {
            mPageProfFieldInput_IN2.setValueEQ(value.toInt());
            mPageLiveFieldInput_IN2.setValueEQ(value.toInt());
        }
    });
    connect(&mWidgetEQ_HP, &EqualizerBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Gain") mPageProfFieldOutput_HP.setValueGAIN(value.toFloat());
        else if(attribute == "Switch") mPageProfFieldOutput_HP.setValueEQ(value.toInt());
    });
    connect(ui->PageLiveSplitterIE_MLO, &QSplitter::splitterMoved, this, [this](int pos, int index){
        Q_UNUSED(pos);
        Q_UNUSED(index);
        QSettings().setValue("M62/PageLiveFactor1", ui->PageLiveSplitterIE_MLO->saveState());
    });
    connect(ui->PageLiveSplitterL_O, &QSplitter::splitterMoved, this, [this](int pos, int index){
        Q_UNUSED(pos);
        Q_UNUSED(index);
        QSettings().setValue("M62/PageLiveFactor2", ui->PageLiveSplitterL_O->saveState());
    });
    connect(ui->PageProfSplitterIL_MO, &QSplitter::splitterMoved, this, [this](int pos, int index){
        Q_UNUSED(pos);
        Q_UNUSED(index);
        QSettings().setValue("M62/PageProfFactor1", ui->PageProfSplitterIL_MO->saveState());
    });
    connect(&APPSHandle, &AppSettingsSubject::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        Q_UNUSED(value);
        if(attribute == "ModifyScaleFactor")
        {
            QSettings().remove("M62/PageLiveFactor1");
            QSettings().remove("M62/PageLiveFactor2");
            QSettings().remove("M62/PageLiveWidth");
            QSettings().remove("M62/PageProfFactor1");
            QSettings().remove("M62/PageProfWidth");
            switch(mCurrentPage)
            {
                case PageMble:
                    adjustGeometryPageMble();
                case PageLive:
                    adjustGeometryPageLive();
                    break;
                case PageTyro:
                    break;
                case PageProf:
                    adjustGeometryPageProf();
                    break;
                default:
                    break;
            }
            mWidgetEQ_IN1.setScaleFactor(value.toDouble());
            mWidgetEQ_IN2.setScaleFactor(value.toDouble());
            mWidgetEQ_HP.setScaleFactor(value.toDouble());
        }
        else if(attribute == "ModifyLanguage")
        {
            if(value == "English")
            {
                ui->PageFctyLabel->setStyleSheet("QLabel { image: url(:/Image/FactoryNoticeEN.png); }");
                ui->PageFctyPushButton1->setStyleSheet("QPushButton { image: url(:/Image/FactoryUpdateOnlineEN.png); }");
                ui->PageFctyPushButton2->setStyleSheet("QPushButton { image: url(:/Image/FactoryUpdateManualEN.png); }");
            }
            else if(value == "Chinese")
            {
                ui->PageFctyLabel->setStyleSheet("QLabel { image: url(:/Image/FactoryNoticeCN.png); }");
                ui->PageFctyPushButton1->setStyleSheet("QPushButton { image: url(:/Image/FactoryUpdateOnlineCN.png); }");
                ui->PageFctyPushButton2->setStyleSheet("QPushButton { image: url(:/Image/FactoryUpdateManualCN.png); }");
            }
        }
    });
}


// PageFcty
void MainWindow_M62::initPageFctyFieldHead()
{
    ui->PageFctyFieldHead->setName("FieldHead").setFont(GLBFHandle.font());
    connect(ui->PageFctyFieldHead, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldHead_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    APPSHandle.addObserver(ui->PageFctyFieldHead);
}
void MainWindow_M62::initPageFctyWidget()
{
}


// PageMble
void MainWindow_M62::initPageMbleFieldHead()
{
    ui->PageMbleFieldHead->setName("FieldHead").setFont(GLBFHandle.font());
    connect(ui->PageMbleFieldHead, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldHead_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    APPSHandle.addObserver(ui->PageMbleFieldHead);
    // Linkage
    connect(ui->PageMbleFieldHead, &FieldHeadS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        if(objectName == "SystemSettings") mSettings.show();
    });
}
void MainWindow_M62::initPageMbleFieldInput()
{
    // field attribute
    ui->PageMbleFieldInput->setName("FieldInput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    QHash<QString, QHash<QString, QString>> language;
    language["Chinese"]["FieldTitle"] = "输入";
    language["English"]["FieldTitle"] = "Input";
    ui->PageMbleFieldInput->registerLanguage(language);
    connect(ui->PageMbleFieldInput, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // base class attribute
    mPageMbleFieldInput_IN1.setName("Input_IN1").setChannelName("IN 1").setWidgetEmitAction(false);
    mPageMbleFieldInput_IN2.setName("Input_IN2").setChannelName("IN 2").setWidgetEmitAction(false);
    mPageMbleFieldInput_AUX.setName("Input_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageMbleFieldInput_BT.setName("Input_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageMbleFieldInput_OTGIN.setName("Input_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    mPageMbleFieldInput_PB12.setName("Input_PB12").setChannelName("Playback 1/2").setWidgetEmitAction(false);
    // derived class attribute
    mPageMbleFieldInput_IN1.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldInput_IN2.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldInput_AUX.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldInput_BT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(-6).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldInput_OTGIN.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldInput_PB12.setFont(GLBFHandle.font()).setGainRange(0, 0, -90, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<OriginBase*> listInputBase;
    listInputBase << &mPageMbleFieldInput_IN1 << &mPageMbleFieldInput_IN2 << &mPageMbleFieldInput_AUX << &mPageMbleFieldInput_BT << &mPageMbleFieldInput_OTGIN << &mPageMbleFieldInput_PB12;
    for(auto element : listInputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageMbleFieldInput->modifyWidgetList(listInputBase);
    // Visible
    listInputBase.clear();
    listInputBase << &mPageMbleFieldInput_IN1 << &mPageMbleFieldInput_IN2 << &mPageMbleFieldInput_AUX << &mPageMbleFieldInput_BT << &mPageMbleFieldInput_OTGIN << &mPageMbleFieldInput_PB12;
    ui->PageMbleFieldInput->setVisibleListDefault(listInputBase);
    // Workspace
    mPageMbleWorkspaceObserverList << ui->PageMbleFieldInput << &mPageMbleFieldInput_IN1 << &mPageMbleFieldInput_IN2 << &mPageMbleFieldInput_AUX << &mPageMbleFieldInput_BT << &mPageMbleFieldInput_OTGIN << &mPageMbleFieldInput_PB12;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageMbleFieldInput_IN1 << &mPageMbleFieldInput_IN2 << &mPageMbleFieldInput_AUX << &mPageMbleFieldInput_BT << &mPageMbleFieldInput_OTGIN << &mPageMbleFieldInput_PB12;
    APPSHandle.addObserver(ui->PageMbleFieldInput).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageMbleFieldEffect()
{
    // field attribute
    ui->PageMbleFieldEffect->setName("FieldEffect").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageMbleFieldEffect_NC.setName("Effect_NC").setChannelName("NC").setWidgetEmitAction(false);
    mPageMbleFieldEffect_Reverb.setName("Effect_Reverb").setChannelName("Reverb").setWidgetEmitAction(false);
    // derived class attribute
    mPageMbleFieldEffect_NC.setFont(GLBFHandle.font()).setParent(this);
    mPageMbleFieldEffect_Reverb.setFont(GLBFHandle.font()).setParent(this);
    // FieldWidgetBase
    QVector<EffectBase*> listEffectBase;
    listEffectBase << &mPageMbleFieldEffect_NC << &mPageMbleFieldEffect_Reverb;
    for(auto element : listEffectBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldEffect_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageMbleFieldEffect->modifyWidgetList(listEffectBase);
    // Visible
    listEffectBase.clear();
    listEffectBase << &mPageMbleFieldEffect_NC << &mPageMbleFieldEffect_Reverb;
    ui->PageMbleFieldEffect->setVisibleListDefault(listEffectBase);
    // Workspace
    mPageMbleWorkspaceObserverList << ui->PageMbleFieldEffect << &mPageMbleFieldEffect_NC << &mPageMbleFieldEffect_Reverb;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageMbleFieldEffect_NC << &mPageMbleFieldEffect_Reverb;
    APPSHandle.addObserver(ui->PageMbleFieldEffect).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageMbleFieldOutput()
{
    // field attribute
    ui->PageMbleFieldOutput->setName("FieldOutput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    QHash<QString, QHash<QString, QString>> language;
    language["Chinese"]["FieldTitle"] = "输出";
    language["English"]["FieldTitle"] = "Output";
    ui->PageMbleFieldOutput->registerLanguage(language);
    // base class attribute
    mPageMbleFieldOutput_HP.setName("Output_HP").setChannelName("HP").setWidgetEmitAction(false);
    mPageMbleFieldOutput_OTGOUT.setName("Output_OTGOUT").setChannelName("OTG OUT").setWidgetEmitAction(false);
    mPageMbleFieldOutput_LB12.setName("Output_LB12").setChannelName("Loopback 1/2").setWidgetEmitAction(false);
    // derived class attribute
    mPageMbleFieldOutput_HP.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(-38).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldOutput_OTGOUT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageMbleFieldOutput_LB12.setFont(GLBFHandle.font()).setGainRange(0, 0, -90, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<OriginBase*> listOutputBase;
    listOutputBase << &mPageMbleFieldOutput_HP << &mPageMbleFieldOutput_OTGOUT << &mPageMbleFieldOutput_LB12;
    for(auto element : listOutputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldOutput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageMbleFieldOutput->modifyWidgetList(listOutputBase);
    // Visible
    listOutputBase.clear();
    listOutputBase << &mPageMbleFieldOutput_HP << &mPageMbleFieldOutput_OTGOUT << &mPageMbleFieldOutput_LB12;
    ui->PageMbleFieldOutput->setVisibleListDefault(listOutputBase);
    // Workspace
    mPageMbleWorkspaceObserverList << ui->PageMbleFieldOutput << &mPageMbleFieldOutput_HP << &mPageMbleFieldOutput_OTGOUT << &mPageMbleFieldOutput_LB12;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageMbleFieldOutput_HP << &mPageMbleFieldOutput_OTGOUT << &mPageMbleFieldOutput_LB12;
    APPSHandle.addObserver(ui->PageMbleFieldOutput).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageMbleWidget()
{
    // WidgetConfigMenu
    ui->PageMbleWidgetConfigMenu->setName("ConfigMenuMble");
    ui->PageMbleWidgetConfigMenu->setFont(GLBFHandle.font());
    connect(ui->PageMbleWidgetConfigMenu, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << ui->PageMbleWidgetConfigMenu;
    APPSHandle.addObserverList(listAppSettingsObserver);
}


// init PageLive
void MainWindow_M62::initPageLiveFieldHead()
{
    ui->PageLiveFieldHead->setName("FieldHead").setFont(GLBFHandle.font());
    connect(ui->PageLiveFieldHead, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldHead_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    APPSHandle.addObserver(ui->PageLiveFieldHead);
    // Linkage
    connect(ui->PageLiveFieldHead, &FieldHeadS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        if(objectName == "SystemSettings") mSettings.show();
    });
}
void MainWindow_M62::initPageLiveFieldInput()
{
    // field attribute
    ui->PageLiveFieldInput->setName("FieldInput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    connect(ui->PageLiveFieldInput, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // base class attribute
    mPageLiveFieldInput_IN1.setName("Input_IN1").setChannelName("IN 1").setWidgetEmitAction(false);
    mPageLiveFieldInput_IN2.setName("Input_IN2").setChannelName("IN 2").setWidgetEmitAction(false);
    mPageLiveFieldInput_AUX.setName("Input_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageLiveFieldInput_BT.setName("Input_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageLiveFieldInput_OTGIN.setName("Input_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    // derived class attribute
    mPageLiveFieldInput_IN1.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageLiveFieldInput_IN2.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageLiveFieldInput_AUX.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageLiveFieldInput_BT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(-6).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageLiveFieldInput_OTGIN.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<InputBase*> listInputBase;
    listInputBase << &mPageLiveFieldInput_IN1 << &mPageLiveFieldInput_IN2 << &mPageLiveFieldInput_AUX << &mPageLiveFieldInput_BT << &mPageLiveFieldInput_OTGIN;
    for(auto element : listInputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageLiveFieldInput->modifyWidgetList(listInputBase);
    // Visible
    listInputBase.clear();
    listInputBase << &mPageLiveFieldInput_IN1 << &mPageLiveFieldInput_IN2 << &mPageLiveFieldInput_AUX << &mPageLiveFieldInput_BT << &mPageLiveFieldInput_OTGIN;
    ui->PageLiveFieldInput->setVisibleListDefault(listInputBase);
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveFieldInput << &mPageLiveFieldInput_IN1 << &mPageLiveFieldInput_IN2 << &mPageLiveFieldInput_AUX << &mPageLiveFieldInput_BT << &mPageLiveFieldInput_OTGIN;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageLiveFieldInput_IN1 << &mPageLiveFieldInput_IN2 << &mPageLiveFieldInput_AUX << &mPageLiveFieldInput_BT << &mPageLiveFieldInput_OTGIN;
    APPSHandle.addObserver(ui->PageLiveFieldInput).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageLiveFieldInput_IN1, &InputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Ducking") mPageLiveWidgetDucking.show();
        else if(attribute.startsWith("Balance"))
        {
            if(attribute == "Balance") mPageLiveFieldMixer_IN12.setBalanceL(value.toInt(), true);
            else mPageLiveFieldMixer_IN12.setBalanceL(value.toInt(), false);
        }
        else if(attribute == "EQ") mWidgetEQ_IN1.show();
    });
    connect(&mPageLiveFieldInput_IN2, &InputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Ducking") mPageLiveWidgetDucking.show();
        else if(attribute.startsWith("Balance"))
        {
            if(attribute == "Balance") mPageLiveFieldMixer_IN12.setBalanceR(value.toInt(), true);
            else mPageLiveFieldMixer_IN12.setBalanceR(value.toInt(), false);
        }
        else if(attribute == "EQ") mWidgetEQ_IN2.show();
    });
}
void MainWindow_M62::initPageLiveFieldMixer()
{
    // field attribute
    QVector<FieldMixerBase1::MixerInfo> listMixerInfo;
    //                    Name    Text     colorDefault       colorHovered       colorSelected
    listMixerInfo.append({"MixA", "Mix A", QColor("#43CF7C"), QColor("#43CF7C"), QColor("#43CF7C")});
    listMixerInfo.append({"MixB", "Mix B", QColor("#00AAFF"), QColor("#00AAFF"), QColor("#00AAFF")});
    listMixerInfo.append({"MixC", "Mix C", QColor("#FFEB3B"), QColor("#FFEB3B"), QColor("#FFEB3B")});
    listMixerInfo.append({"MixD", "Mix D", QColor("#FF4C00"), QColor("#FF4C00"), QColor("#FF4C00")});
    listMixerInfo.append({"MixE", "Mix E", QColor("#C267E6"), QColor("#C267E6"), QColor("#C267E6")});
    ui->PageLiveFieldMixer->setName("FieldMixer").modifyMixerList(listMixerInfo).setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(true).setFont(GLBFHandle.font());
    connect(ui->PageLiveFieldMixer, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // base class attribute
    mPageLiveFieldMixer_IN12.setName("Mixer_IN12").setChannelName("IN").setWidgetEmitAction(false);
    mPageLiveFieldMixer_AUX.setName("Mixer_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageLiveFieldMixer_BT.setName("Mixer_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageLiveFieldMixer_OTGIN.setName("Mixer_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    mPageLiveFieldMixer_FX.setName("Mixer_FX").setChannelName("FX").setWidgetEmitAction(false);
    mPageLiveFieldMixer_PB12.setName("Mixer_PB12").setChannelName("Playback 1/2").setWidgetEmitAction(false);
    mPageLiveFieldMixer_PB34.setName("Mixer_PB34").setChannelName("Playback 3/4").setWidgetEmitAction(false);
    mPageLiveFieldMixer_PB56.setName("Mixer_PB56").setChannelName("Playback 5/6").setWidgetEmitAction(false);
    mPageLiveFieldMixer_PB78.setName("Mixer_PB78").setChannelName("Playback 7/8").setWidgetEmitAction(false);
    mPageLiveFieldMixer_PB910.setName("Mixer_PB910").setChannelName("Playback 9/10").setWidgetEmitAction(false);
    // derived class attribute
    mPageLiveFieldMixer_IN12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_AUX.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_BT.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_OTGIN.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_FX.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_PB12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_PB34.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_PB56.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_PB78.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageLiveFieldMixer_PB910.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    // FieldWidgetBase
    QVector<MixerBase*> listMixerBase;
    listMixerBase << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT << &mPageLiveFieldMixer_OTGIN << &mPageLiveFieldMixer_FX;
    listMixerBase << &mPageLiveFieldMixer_PB12 << &mPageLiveFieldMixer_PB34 << &mPageLiveFieldMixer_PB56 << &mPageLiveFieldMixer_PB78 << &mPageLiveFieldMixer_PB910;
    for(auto element : listMixerBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageLiveFieldMixer->modifyWidgetList(listMixerBase);
    // Visible
    QVector<QString> mixerList=ui->PageLiveFieldMixer->getSupportedMixerName();
    for(auto element : mixerList)
    {
        listMixerBase.clear();
        if(element == "MixA") listMixerBase << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT << &mPageLiveFieldMixer_OTGIN << &mPageLiveFieldMixer_FX << &mPageLiveFieldMixer_PB12 << &mPageLiveFieldMixer_PB34 << &mPageLiveFieldMixer_PB56 << &mPageLiveFieldMixer_PB78 << &mPageLiveFieldMixer_PB910;
        else if(element == "MixB") listMixerBase << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT << &mPageLiveFieldMixer_OTGIN;
        else if(element == "MixC") listMixerBase << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT;
        else if(element == "MixD") listMixerBase << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX;
        else if(element == "MixE") listMixerBase << &mPageLiveFieldMixer_IN12;
        ui->PageLiveFieldMixer->setVisibleListDefault(element, listMixerBase);
    }
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveFieldMixer << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT << &mPageLiveFieldMixer_OTGIN << &mPageLiveFieldMixer_FX;
    mPageLiveWorkspaceObserverList << &mPageLiveFieldMixer_PB12 << &mPageLiveFieldMixer_PB34 << &mPageLiveFieldMixer_PB56 << &mPageLiveFieldMixer_PB78 << &mPageLiveFieldMixer_PB910;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageLiveFieldMixer_IN12 << &mPageLiveFieldMixer_AUX << &mPageLiveFieldMixer_BT << &mPageLiveFieldMixer_OTGIN << &mPageLiveFieldMixer_FX;
    listAppSettingsObserver << &mPageLiveFieldMixer_PB12 << &mPageLiveFieldMixer_PB34 << &mPageLiveFieldMixer_PB56 << &mPageLiveFieldMixer_PB78 << &mPageLiveFieldMixer_PB910;
    APPSHandle.addObserver(ui->PageLiveFieldMixer).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageLiveFieldEffect()
{
    // field attribute
    ui->PageLiveFieldEffect->setName("FieldEffect").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageLiveFieldEffect_Integration.setName("Effect_Integration").setChannelName("Effect_Integration").setWidgetEmitAction(false);
    // derived class attribute
    mPageLiveFieldEffect_Integration.setFont(GLBFHandle.font()).setParent(this);
    // FieldWidgetBase
    QVector<EffectBase*> listEffectBase;
    listEffectBase << &mPageLiveFieldEffect_Integration;
    for(auto element : listEffectBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldEffect_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageLiveFieldEffect->modifyWidgetList(listEffectBase);
    // Visible
    listEffectBase.clear();
    listEffectBase << &mPageLiveFieldEffect_Integration;
    ui->PageLiveFieldEffect->setVisibleListDefault(listEffectBase);
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveFieldEffect << &mPageLiveFieldEffect_Integration;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageLiveFieldEffect_Integration;
    APPSHandle.addObserver(ui->PageLiveFieldEffect).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageLiveFieldLoopback()
{
    // field attribute
    ui->PageLiveFieldLoopback->setName("FieldLoopback").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageLiveFieldLoopback_LB12.setName("Loopback_LB12").setChannelName("Loopback 1/2").setWidgetEmitAction(false);
    mPageLiveFieldLoopback_LB34.setName("Loopback_LB34").setChannelName("Loopback 3/4").setWidgetEmitAction(false);
    mPageLiveFieldLoopback_LB56.setName("Loopback_LB56").setChannelName("Loopback 5/6").setWidgetEmitAction(false);
    mPageLiveFieldLoopback_LB78.setName("Loopback_LB78").setChannelName("Loopback 7/8").setWidgetEmitAction(false);
    // derived class attribute
    QVector<QString> listSourceMixer, listSourceInput, listSourcePlayback, listSourceEffect;
    listSourceMixer << ui->PageLiveFieldMixer->getSupportedMixerText();
    listSourceInput << "IN 1" << "IN 2" << "IN 1+2" << "AUX" << "BT" << "OTG IN";
    listSourcePlayback << "Playback 1/2" << "Playback 3/4" << "Playback 5/6" << "Playback 7/8" << "Playback 9/10";
    listSourceEffect << "FX";
    mPageLiveFieldLoopback_LB12.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldLoopback_LB34.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldLoopback_LB56.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldLoopback_LB78.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldLoopback_LB12.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageLiveFieldLoopback_LB34.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageLiveFieldLoopback_LB56.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageLiveFieldLoopback_LB78.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    // FieldWidgetBase
    QVector<LoopbackBase*> listLoopbackBase;
    listLoopbackBase << &mPageLiveFieldLoopback_LB12 << &mPageLiveFieldLoopback_LB34 << &mPageLiveFieldLoopback_LB56 << &mPageLiveFieldLoopback_LB78;
    for(auto element : listLoopbackBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldLoopback_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageLiveFieldLoopback->modifyWidgetList(listLoopbackBase);
    // Visible
    listLoopbackBase.clear();
    listLoopbackBase << &mPageLiveFieldLoopback_LB12 << &mPageLiveFieldLoopback_LB34 << &mPageLiveFieldLoopback_LB56 << &mPageLiveFieldLoopback_LB78;
    ui->PageLiveFieldLoopback->setVisibleListDefault(listLoopbackBase);
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveFieldLoopback << &mPageLiveFieldLoopback_LB12 << &mPageLiveFieldLoopback_LB34 << &mPageLiveFieldLoopback_LB56 << &mPageLiveFieldLoopback_LB78;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageLiveFieldLoopback_LB12 << &mPageLiveFieldLoopback_LB34 << &mPageLiveFieldLoopback_LB56 << &mPageLiveFieldLoopback_LB78;
    APPSHandle.addObserver(ui->PageLiveFieldLoopback).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageLiveFieldLoopback_LB12, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldLoopback_LB12.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageLiveFieldLoopback_LB34, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldLoopback_LB34.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageLiveFieldLoopback_LB56, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldLoopback_LB56.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageLiveFieldLoopback_LB78, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldLoopback_LB78.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
}
void MainWindow_M62::initPageLiveFieldOutput()
{
    // field attribute
    ui->PageLiveFieldOutput->setName("FieldOutput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageLiveFieldOutput_HP.setName("Output_HP").setChannelName("HP").setWidgetEmitAction(false);
    mPageLiveFieldOutput_OTGOUT.setName("Output_OTGOUT").setChannelName("OTG OUT").setWidgetEmitAction(false);
    // derived class attribute
    QVector<QString> listSourceMixer, listSourceInput, listSourcePlayback, listSourceEffect;
    listSourceMixer << ui->PageLiveFieldMixer->getSupportedMixerText();
    listSourceInput << "IN 1" << "IN 2" << "IN 1+2" << "AUX" << "BT" << "OTG IN";
    listSourcePlayback << "Playback 1/2" << "Playback 3/4" << "Playback 5/6" << "Playback 7/8" << "Playback 9/10";
    listSourceEffect << "FX";
    mPageLiveFieldOutput_HP.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldOutput_OTGOUT.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback).addAudioSource("Effect", listSourceEffect);
    mPageLiveFieldOutput_HP.setGainRange(9, -10, -52, -90).setGainDefault(-38).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageLiveFieldOutput_OTGOUT.setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<OutputBase*> listOutputBase;
    listOutputBase << &mPageLiveFieldOutput_HP << &mPageLiveFieldOutput_OTGOUT;
    for(auto element : listOutputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldOutput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageLiveFieldOutput->modifyWidgetList(listOutputBase);
    // Visible
    listOutputBase.clear();
    listOutputBase << &mPageLiveFieldOutput_HP << &mPageLiveFieldOutput_OTGOUT;
    ui->PageLiveFieldOutput->setVisibleListDefault(listOutputBase);
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveFieldOutput << &mPageLiveFieldOutput_HP << &mPageLiveFieldOutput_OTGOUT;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageLiveFieldOutput_HP << &mPageLiveFieldOutput_OTGOUT;
    APPSHandle.addObserver(ui->PageLiveFieldOutput).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageLiveFieldOutput_HP, &OutputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldOutput_HP.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageLiveFieldOutput_OTGOUT, &OutputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageLiveFieldOutput_OTGOUT.setAudioSourceColor(ui->PageLiveFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageLiveFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
}
void MainWindow_M62::initPageLiveWidget()
{
    // WidgetConfigMenu
    ui->PageLiveWidgetConfigMenu->setName("ConfigMenuLive");
    ui->PageLiveWidgetConfigMenu->setFont(GLBFHandle.font());
    connect(ui->PageLiveWidgetConfigMenu, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // WidgetDucking
    mPageLiveWidgetDucking.setParent(this);
    mPageLiveWidgetDucking.hide();
    mPageLiveWidgetDucking.setName("Ducking");
    mPageLiveWidgetDucking.setFont(GLBFHandle.font());
    connect(&mPageLiveWidgetDucking, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // Workspace
    mPageLiveWorkspaceObserverList << ui->PageLiveWidgetConfigMenu << &mPageLiveWidgetDucking;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << ui->PageLiveWidgetConfigMenu << &mPageLiveWidgetDucking;
    APPSHandle.addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageLiveWidgetDucking, &M62_PrivateWidget1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "IN1WorkState")
        {
            mPageLiveFieldInput_IN1.setValueDucking(value.toInt());
        }
        else if(attribute == "IN2WorkState")
        {
            mPageLiveFieldInput_IN2.setValueDucking(value.toInt());
        }
    });
}


// init PageTyro
void MainWindow_M62::initPageTyroFieldTop()
{
    // field attribute
    ui->PageTyroFieldTop->setName("FieldTop").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageTyroFieldTop_IN1.setName("Input_IN1").setChannelName("IN1").setWidgetEmitAction(false);
    mPageTyroFieldTop_IN2.setName("Input_IN2").setChannelName("IN2").setWidgetEmitAction(false);
    mPageTyroFieldTop_AUX.setName("Input_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageTyroFieldTop_BT.setName("Input_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageTyroFieldTop_OTGIN.setName("Input_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    mPageTyroFieldTop_PB12.setName("Mixer_PB12").setChannelName("Playback 1/2").setWidgetEmitAction(false);
    mPageTyroFieldTop_PB34.setName("Mixer_PB34").setChannelName("Playback 3/4").setWidgetEmitAction(false);
    // derived class attribute
    mPageTyroFieldTop_IN1.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldTop_IN2.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldTop_AUX.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldTop_BT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(-6).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldTop_OTGIN.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldTop_PB12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageTyroFieldTop_PB34.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    // FieldWidgetBase
    QVector<OriginBase*> listOriginBase;
    listOriginBase << &mPageTyroFieldTop_IN1 << &mPageTyroFieldTop_IN2 << &mPageTyroFieldTop_AUX << &mPageTyroFieldTop_BT << &mPageTyroFieldTop_OTGIN;
    for(auto element : listOriginBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldTop_PB12 << &mPageTyroFieldTop_PB34;
    for(auto element : listOriginBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldTop_IN1 << &mPageTyroFieldTop_IN2 << &mPageTyroFieldTop_AUX << &mPageTyroFieldTop_BT << &mPageTyroFieldTop_OTGIN << &mPageTyroFieldTop_PB12 << &mPageTyroFieldTop_PB34;
    ui->PageTyroFieldTop->modifyWidgetList(listOriginBase);
    // Visible
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldTop_IN1 << &mPageTyroFieldTop_IN2 << &mPageTyroFieldTop_AUX << &mPageTyroFieldTop_BT << &mPageTyroFieldTop_OTGIN << &mPageTyroFieldTop_PB12 << &mPageTyroFieldTop_PB34;
    ui->PageTyroFieldTop->setVisibleListDefault(listOriginBase);
    // Workspace
    mPageTyroWorkspaceObserverList << ui->PageTyroFieldTop << &mPageTyroFieldTop_IN1 << &mPageTyroFieldTop_IN2 << &mPageTyroFieldTop_AUX << &mPageTyroFieldTop_BT << &mPageTyroFieldTop_OTGIN << &mPageTyroFieldTop_PB12 << &mPageTyroFieldTop_PB34;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageTyroFieldTop_IN1 << &mPageTyroFieldTop_IN2 << &mPageTyroFieldTop_AUX << &mPageTyroFieldTop_BT << &mPageTyroFieldTop_OTGIN << &mPageTyroFieldTop_PB12 << &mPageTyroFieldTop_PB34;
    APPSHandle.addObserver(ui->PageTyroFieldTop).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageTyroFieldTop_PB12, &OriginBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Gain")
        {
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB12", "MixA_" + attribute, 0);
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB12", "MixB_" + attribute, value);
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB12", "MixC_" + attribute, value);
        }
    });
    connect(&mPageTyroFieldTop_PB34, &OriginBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Gain")
        {
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB34", "MixA_" + attribute, 0);
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB34", "MixB_" + attribute, value);
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB34", "MixC_" + attribute, value);
        }
    });
}
void MainWindow_M62::initPageTyroFieldBottom()
{
    // field attribute
    ui->PageTyroFieldBottom->setName("FieldBottom").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageTyroFieldBottom_ConfigMenu.setName("ConfigMenuTyro").setChannelName("ConfigMenu").setWidgetEmitAction(false);
    mPageTyroFieldBottom_Effect.setName("Effect").setChannelName("Effect").setWidgetEmitAction(false);
    mPageTyroFieldBottom_LB12.setName("Loopback_LB12").setChannelName("Loopback 1/2").setWidgetEmitAction(false);
    mPageTyroFieldBottom_HP.setName("Output_HP").setChannelName("HP").setWidgetEmitAction(false);
    mPageTyroFieldBottom_OTGOUT.setName("Output_OTGOUT").setChannelName("OTG OUT").setWidgetEmitAction(false);
    // derived class attribute
    mPageTyroFieldBottom_ConfigMenu.setFont(GLBFHandle.font());
    mPageTyroFieldBottom_Effect.setFont(GLBFHandle.font());
    mPageTyroFieldBottom_LB12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageTyroFieldBottom_HP.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(-38).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    mPageTyroFieldBottom_OTGOUT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setParent(this);
    // FieldWidgetBase
    connect(&mPageTyroFieldBottom_ConfigMenu, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mPageTyroFieldBottom_Effect, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldEffect_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mPageTyroFieldBottom_Effect, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    QVector<OriginBase*> listOriginBase;
    listOriginBase << &mPageTyroFieldBottom_LB12;
    for(auto element : listOriginBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldLoopback_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldBottom_HP << &mPageTyroFieldBottom_OTGOUT;
    for(auto element : listOriginBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldOutput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldBottom_ConfigMenu << &mPageTyroFieldBottom_Effect << &mPageTyroFieldBottom_LB12 << &mPageTyroFieldBottom_HP << &mPageTyroFieldBottom_OTGOUT;
    ui->PageTyroFieldBottom->modifyWidgetList(listOriginBase);
    // Visible
    listOriginBase.clear();
    listOriginBase << &mPageTyroFieldBottom_ConfigMenu << &mPageTyroFieldBottom_Effect << &mPageTyroFieldBottom_LB12 << &mPageTyroFieldBottom_HP << &mPageTyroFieldBottom_OTGOUT;
    ui->PageTyroFieldBottom->setVisibleListDefault(listOriginBase);
    // Workspace
    mPageTyroWorkspaceObserverList << ui->PageTyroFieldBottom << &mPageTyroFieldBottom_ConfigMenu << &mPageTyroFieldBottom_Effect << &mPageTyroFieldBottom_LB12 << &mPageTyroFieldBottom_HP << &mPageTyroFieldBottom_OTGOUT;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageTyroFieldBottom_ConfigMenu << &mPageTyroFieldBottom_Effect << &mPageTyroFieldBottom_LB12 << &mPageTyroFieldBottom_HP << &mPageTyroFieldBottom_OTGOUT;
    APPSHandle.addObserver(ui->PageTyroFieldBottom).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageTyroFieldBottom_ConfigMenu, &OriginBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "Professional") setPageToActive(PageLive);
    });
}


// init PageProf
void MainWindow_M62::initPageProfFieldHead()
{
    ui->PageProfFieldHead->setName("FieldHead").setFont(GLBFHandle.font());
    connect(ui->PageProfFieldHead, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldHead_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    APPSHandle.addObserver(ui->PageProfFieldHead);
    // Linkage
    connect(ui->PageProfFieldHead, &FieldHeadS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        if(objectName == "SystemSettings") mSettings.show();
    });
}
void MainWindow_M62::initPageProfFieldInput()
{
    // field attribute
    ui->PageProfFieldInput->setName("FieldInput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    connect(ui->PageProfFieldInput, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // base class attribute
    mPageProfFieldInput_IN1.setName("Input_IN1").setChannelName("IN 1").setWidgetEmitAction(false);
    mPageProfFieldInput_IN2.setName("Input_IN2").setChannelName("IN 2").setWidgetEmitAction(false);
    mPageProfFieldInput_AUX.setName("Input_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageProfFieldInput_BT.setName("Input_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageProfFieldInput_OTGIN.setName("Input_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    // derived class attribute
    mPageProfFieldInput_IN1.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageProfFieldInput_IN2.setFont(GLBFHandle.font()).setGainRange(0, 88).setGainDefault(26).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageProfFieldInput_AUX.setFont(GLBFHandle.font()).setGainRange(9, -10, -52, -90).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageProfFieldInput_BT.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(-6).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    mPageProfFieldInput_OTGIN.setFont(GLBFHandle.font()).setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<InputBase*> listInputBase;
    listInputBase << &mPageProfFieldInput_IN1 << &mPageProfFieldInput_IN2 << &mPageProfFieldInput_AUX << &mPageProfFieldInput_BT << &mPageProfFieldInput_OTGIN;
    for(auto element : listInputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldInput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageProfFieldInput->modifyWidgetList(listInputBase);
    // Visible
    listInputBase.clear();
    listInputBase << &mPageProfFieldInput_IN1 << &mPageProfFieldInput_IN2 << &mPageProfFieldInput_AUX << &mPageProfFieldInput_BT << &mPageProfFieldInput_OTGIN;
    ui->PageProfFieldInput->setVisibleListDefault(listInputBase);
    // Workspace
    mPageProfWorkspaceObserverList << ui->PageProfFieldInput << &mPageProfFieldInput_IN1 << &mPageProfFieldInput_IN2 << &mPageProfFieldInput_AUX << &mPageProfFieldInput_BT << &mPageProfFieldInput_OTGIN;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageProfFieldInput_IN1 << &mPageProfFieldInput_IN2 << &mPageProfFieldInput_AUX << &mPageProfFieldInput_BT << &mPageProfFieldInput_OTGIN;
    APPSHandle.addObserver(ui->PageProfFieldInput).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageProfFieldInput_IN1, &InputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "EQ") mWidgetEQ_IN1.show();
    });
    connect(&mPageProfFieldInput_IN2, &InputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "EQ") mWidgetEQ_IN2.show();
    });
}
void MainWindow_M62::initPageProfFieldMixer()
{
    // field attribute
    QVector<FieldMixerBase1::MixerInfo> listMixerInfo;
    //                    Name    Text     colorDefault       colorHovered       colorSelected
    listMixerInfo.append({"MixA", "Mix A", QColor("#43CF7C"), QColor("#43CF7C"), QColor("#43CF7C")});
    listMixerInfo.append({"MixB", "Mix B", QColor("#00AAFF"), QColor("#00AAFF"), QColor("#00AAFF")});
    listMixerInfo.append({"MixC", "Mix C", QColor("#FFEB3B"), QColor("#FFEB3B"), QColor("#FFEB3B")});
    listMixerInfo.append({"MixD", "Mix D", QColor("#FF4C00"), QColor("#FF4C00"), QColor("#FF4C00")});
    listMixerInfo.append({"MixE", "Mix E", QColor("#C267E6"), QColor("#C267E6"), QColor("#C267E6")});
    ui->PageProfFieldMixer->setName("FieldMixer").modifyMixerList(listMixerInfo).setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(true).setFont(GLBFHandle.font());
    connect(ui->PageProfFieldMixer, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // base class attribute
    mPageProfFieldMixer_IN12.setName("Mixer_IN12").setChannelName("IN").setWidgetEmitAction(false);
    mPageProfFieldMixer_AUX.setName("Mixer_AUX").setChannelName("AUX").setWidgetEmitAction(false);
    mPageProfFieldMixer_BT.setName("Mixer_BT").setChannelName("BT").setWidgetEmitAction(false);
    mPageProfFieldMixer_OTGIN.setName("Mixer_OTGIN").setChannelName("OTG IN").setWidgetEmitAction(false);
    mPageProfFieldMixer_PB12.setName("Mixer_PB12").setChannelName("Playback 1/2").setWidgetEmitAction(false);
    mPageProfFieldMixer_PB34.setName("Mixer_PB34").setChannelName("Playback 3/4").setWidgetEmitAction(false);
    mPageProfFieldMixer_PB56.setName("Mixer_PB56").setChannelName("Playback 5/6").setWidgetEmitAction(false);
    mPageProfFieldMixer_PB78.setName("Mixer_PB78").setChannelName("Playback 7/8").setWidgetEmitAction(false);
    mPageProfFieldMixer_PB910.setName("Mixer_PB910").setChannelName("Playback 9/10").setWidgetEmitAction(false);
    // derived class attribute
    mPageProfFieldMixer_IN12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setLinkDefaultState(false).setParent(this);
    mPageProfFieldMixer_AUX.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_BT.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_OTGIN.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_PB12.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_PB34.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_PB56.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_PB78.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    mPageProfFieldMixer_PB910.setFont(GLBFHandle.font()).setChannelNameEditable(false).setParent(this);
    // FieldWidgetBase
    QVector<MixerBase*> listMixerBase;
    listMixerBase << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT << &mPageProfFieldMixer_OTGIN;
    listMixerBase << &mPageProfFieldMixer_PB12 << &mPageProfFieldMixer_PB34 << &mPageProfFieldMixer_PB56 << &mPageProfFieldMixer_PB78 << &mPageProfFieldMixer_PB910;
    for(auto element : listMixerBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldMixer_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageProfFieldMixer->modifyWidgetList(listMixerBase);
    // Visible
    QVector<QString> mixerList=ui->PageProfFieldMixer->getSupportedMixerName();
    for(auto element : mixerList)
    {
        listMixerBase.clear();
        if(element == "MixA") listMixerBase << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT << &mPageProfFieldMixer_OTGIN << &mPageProfFieldMixer_PB12 << &mPageProfFieldMixer_PB34 << &mPageProfFieldMixer_PB56 << &mPageProfFieldMixer_PB78 << &mPageProfFieldMixer_PB910;
        else if(element == "MixB") listMixerBase << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT << &mPageProfFieldMixer_OTGIN;
        else if(element == "MixC") listMixerBase << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT;
        else if(element == "MixD") listMixerBase << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX;
        else if(element == "MixE") listMixerBase << &mPageProfFieldMixer_IN12;
        ui->PageProfFieldMixer->setVisibleListDefault(element, listMixerBase);
    }
    // Workspace
    mPageProfWorkspaceObserverList << ui->PageProfFieldMixer << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT << &mPageProfFieldMixer_OTGIN;
    mPageProfWorkspaceObserverList << &mPageProfFieldMixer_PB12 << &mPageProfFieldMixer_PB34 << &mPageProfFieldMixer_PB56 << &mPageProfFieldMixer_PB78 << &mPageProfFieldMixer_PB910;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageProfFieldMixer_IN12 << &mPageProfFieldMixer_AUX << &mPageProfFieldMixer_BT << &mPageProfFieldMixer_OTGIN;
    listAppSettingsObserver << &mPageProfFieldMixer_PB12 << &mPageProfFieldMixer_PB34 << &mPageProfFieldMixer_PB56 << &mPageProfFieldMixer_PB78 << &mPageProfFieldMixer_PB910;
    APPSHandle.addObserver(ui->PageProfFieldMixer).addObserverList(listAppSettingsObserver);
}
void MainWindow_M62::initPageProfFieldLoopback()
{
    // field attribute
    ui->PageProfFieldLoopback->setName("FieldLoopback").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageProfFieldLoopback_LB12.setName("Loopback_LB12").setChannelName("Loopback 1/2").setWidgetEmitAction(false);
    mPageProfFieldLoopback_LB34.setName("Loopback_LB34").setChannelName("Loopback 3/4").setWidgetEmitAction(false);
    mPageProfFieldLoopback_LB56.setName("Loopback_LB56").setChannelName("Loopback 5/6").setWidgetEmitAction(false);
    mPageProfFieldLoopback_LB78.setName("Loopback_LB78").setChannelName("Loopback 7/8").setWidgetEmitAction(false);
    // derived class attribute
    QVector<QString> listSourceMixer, listSourceInput, listSourcePlayback;
    listSourceMixer << ui->PageProfFieldMixer->getSupportedMixerText();
    listSourceInput << "IN 1" << "IN 2" << "IN 1+2" << "AUX" << "BT" << "OTG IN";
    listSourcePlayback << "Playback 1/2" << "Playback 3/4" << "Playback 5/6" << "Playback 7/8" << "Playback 9/10";
    mPageProfFieldLoopback_LB12.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldLoopback_LB34.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldLoopback_LB56.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldLoopback_LB78.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldLoopback_LB12.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageProfFieldLoopback_LB34.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageProfFieldLoopback_LB56.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    mPageProfFieldLoopback_LB78.setChannelNameEditable(false).setMuteAffectGain(true).setGainWidgetDisable(-90).setParent(this);
    // FieldWidgetBase
    QVector<LoopbackBase*> listLoopbackBase;
    listLoopbackBase << &mPageProfFieldLoopback_LB12 << &mPageProfFieldLoopback_LB34 << &mPageProfFieldLoopback_LB56 << &mPageProfFieldLoopback_LB78;
    for(auto element : listLoopbackBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldLoopback_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageProfFieldLoopback->modifyWidgetList(listLoopbackBase);
    // Visible
    listLoopbackBase.clear();
    listLoopbackBase << &mPageProfFieldLoopback_LB12 << &mPageProfFieldLoopback_LB34 << &mPageProfFieldLoopback_LB56 << &mPageProfFieldLoopback_LB78;
    ui->PageProfFieldLoopback->setVisibleListDefault(listLoopbackBase);
    // Workspace
    mPageProfWorkspaceObserverList << ui->PageProfFieldLoopback << &mPageProfFieldLoopback_LB12 << &mPageProfFieldLoopback_LB34 << &mPageProfFieldLoopback_LB56 << &mPageProfFieldLoopback_LB78;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageProfFieldLoopback_LB12 << &mPageProfFieldLoopback_LB34 << &mPageProfFieldLoopback_LB56 << &mPageProfFieldLoopback_LB78;
    APPSHandle.addObserver(ui->PageProfFieldLoopback).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageProfFieldLoopback_LB12, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldLoopback_LB12.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageProfFieldLoopback_LB34, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldLoopback_LB34.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageProfFieldLoopback_LB56, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldLoopback_LB56.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
    connect(&mPageProfFieldLoopback_LB78, &LoopbackBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldLoopback_LB78.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
}
void MainWindow_M62::initPageProfFieldOutput()
{
    // field attribute
    ui->PageProfFieldOutput->setName("FieldOutput").setAdditionButtonWeight(90, 25).setWidgetAreaVisible(false).setAdditionVisible(false).setFont(GLBFHandle.font());
    // base class attribute
    mPageProfFieldOutput_HP.setName("Output_HP").setChannelName("HP").setWidgetEmitAction(false);
    mPageProfFieldOutput_OTGOUT.setName("Output_OTGOUT").setChannelName("OTG OUT").setWidgetEmitAction(false);
    // derived class attribute
    QVector<QString> listSourceMixer, listSourceInput, listSourcePlayback;
    listSourceMixer << ui->PageProfFieldMixer->getSupportedMixerText();
    listSourceInput << "IN 1" << "IN 2" << "IN 1+2" << "AUX" << "BT" << "OTG IN";
    listSourcePlayback << "Playback 1/2" << "Playback 3/4" << "Playback 5/6" << "Playback 7/8" << "Playback 9/10";
    mPageProfFieldOutput_HP.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldOutput_OTGOUT.setFont(GLBFHandle.font()).setAudioSourceDefault("Mix A").addAudioSource("Mixer", listSourceMixer).addAudioSource("Input", listSourceInput).addAudioSource("Playback", listSourcePlayback);
    mPageProfFieldOutput_HP.setGainRange(9, -10, -52, -90).setGainDefault(-38).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setEqualizer(&mWidgetEQ_HP).setParent(this);
    mPageProfFieldOutput_OTGOUT.setGainRange(0, -10, -89, -89).setGainDefault(0).setChannelNameEditable(false).setGainAffectMute(true).setOverlay(true).setParent(this);
    // FieldWidgetBase
    QVector<OutputBase*> listOutputBase;
    listOutputBase << &mPageProfFieldOutput_HP << &mPageProfFieldOutput_OTGOUT;
    for(auto element : listOutputBase)
    {
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_fieldOutput_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    ui->PageProfFieldOutput->modifyWidgetList(listOutputBase);
    // Visible
    listOutputBase.clear();
    listOutputBase << &mPageProfFieldOutput_HP << &mPageProfFieldOutput_OTGOUT;
    ui->PageProfFieldOutput->setVisibleListDefault(listOutputBase);
    // Workspace
    mPageProfWorkspaceObserverList << ui->PageProfFieldOutput << &mPageProfFieldOutput_HP << &mPageProfFieldOutput_OTGOUT;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << &mPageProfFieldOutput_HP << &mPageProfFieldOutput_OTGOUT;
    APPSHandle.addObserver(ui->PageProfFieldOutput).addObserverList(listAppSettingsObserver);
    // Linkage
    connect(&mPageProfFieldOutput_HP, &OutputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldOutput_HP.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
        else if(attribute == "GAINLeft") mWidgetEQ_HP.setGainOutputLeft(value.toFloat());
        else if(attribute == "EQ") mWidgetEQ_HP.show();
    });
    connect(&mPageProfFieldOutput_OTGOUT, &OutputBase::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "AudioSource") mPageProfFieldOutput_OTGOUT.setAudioSourceColor(ui->PageProfFieldMixer->getSelectedColorByMixerText(value).isValid() ? ui->PageProfFieldMixer->getSelectedColorByMixerText(value) : QColor(216, 216, 216));
    });
}
void MainWindow_M62::initPageProfWidget()
{
    // WidgetConfigMenu
    ui->PageProfWidgetConfigMenu->setName("ConfigMenuProf");
    ui->PageProfWidgetConfigMenu->setFont(GLBFHandle.font());
    connect(ui->PageProfWidgetConfigMenu, SIGNAL(attributeChanged(QString, QString, QString)), &mDevice, SLOT(in_Others_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    // Workspace
    mPageProfWorkspaceObserverList << ui->PageProfWidgetConfigMenu;
    // AppSettings
    QVector<AppSettingsObserver*> listAppSettingsObserver;
    listAppSettingsObserver << ui->PageProfWidgetConfigMenu;
    APPSHandle.addObserverList(listAppSettingsObserver);
}


// do
void MainWindow_M62::doNewFrameReceived_cmdS(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Sc_running:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Sa_connect_state:
                    setAuthResult(frame.data);
                    break;
                case DeviceM62::Sa_work_state:
                    break;
                case DeviceM62::Sa_battery_Electricity:
                    ui->PageLiveFieldHead->setBatteryValue(frame.data);
                    ui->PageProfFieldHead->setBatteryValue(frame.data);
                    break;
                case DeviceM62::Sa_battery_ChargeState:
                    ui->PageLiveFieldHead->setBatteryCharging(frame.data);
                    ui->PageProfFieldHead->setBatteryCharging(frame.data);
                    break;
                case DeviceM62::Sa_BT:
                    mSettingsDevice.setBluetooth(frame.data);
                    break;
                case DeviceM62::Sa_brightness:
                    if(frame.data == 0) mSettingsDevice.setBrightness("Dim");
                    else if(frame.data == 1) mSettingsDevice.setBrightness("Normal");
                    else if(frame.data == 2) mSettingsDevice.setBrightness("Bright");
                    break;
                case DeviceM62::Sa_OTGCharge:
                    mSettingsDevice.setOtgDirection(frame.data);
                    break;
                case DeviceM62::Sa_USBCCharge:
                    if(frame.data == 1) mSettingsDevice.setUsbCCharging("Charge");
                    else if(frame.data == 2) mSettingsDevice.setUsbCCharging("Discharge");
                    else if(frame.data == 3) mSettingsDevice.setUsbCCharging("Disabled");
                    break;
                case DeviceM62::Sa_Stdandby:
                    mSettingsDevice.setAutoPowerOff(frame.data);
                    break;
                case DeviceM62::Sa_MainButton_Single:
                    mSettingsDevice.setMainButtonSingleClick(frame.data);
                    break;
                case DeviceM62::Sa_MainButton_Double:
                    mSettingsDevice.setMainButtonDoubleClick(frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Sc_infomation:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Sa_hardware_version:
                    mSettingsAbout.setHardwareVersion(QString("%1.%2").arg(frame.data / 100).arg(frame.data % 100, 2, 10, QChar('0')));
                    break;
                case DeviceM62::Sa_software_version:
                    mVersionList[0] |= 0x01;
                    mVersionList[1] = frame.data;
                    break;
                case DeviceM62::Sa_otg_version:
                    mVersionList[0] |= 0x02;
                    mVersionList[2] = frame.data;
                    break;
                case DeviceM62::Sa_mobile_version:
                    mVersionList[0] |= 0x04;
                    mVersionList[3] = frame.data;
                    break;
                case DeviceM62::Sa_pclive_version:
                    mVersionList[0] |= 0x08;
                    mVersionList[4] = frame.data;
                    break;
                case DeviceM62::Sa_profression_version:
                    mVersionList[0] |= 0x10;
                    mVersionList[5] = frame.data;
                    break;
                default:
                    break;
            }
            if(mVersionList[0] == 0x1f)
            {
                mVersionList[0] = 0xffff;
                QString version;
                for(QVector<int>::const_iterator it=mVersionList.constBegin() + 1; it != mVersionList.constEnd(); ++it)
                {
                    version.append(QString("%1.").arg(*it, 2, 16, QChar('0')));
                }
                UPDATER_INSTANCE(Firmware)->setCurVersion(version.chopped(1));
            }
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdI(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Ic_ch_1:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    increaseFrameTick();
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN1.setVolumeMeter(frame.data);
                            mPageLiveWidgetDucking.setVolumeMeterLeft(frame.data);
                            mPageLiveFieldMixer_IN12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "IN 1") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "IN 1") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "IN 1") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "IN 1") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN1.setVolumeMeter(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN1.setVolumeMeter(frame.data);
                            mPageProfFieldMixer_IN12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "IN 1") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "IN 1") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "IN 1") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "IN 1") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    mWidgetEQ_IN1.setVolumeMeterInputLeft(frame.data);
                    break;
                case DeviceM62::Ia_mic:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(frame.data == 1) mPageLiveFieldInput_IN1.setValueMIC("Mic1");
                            else if(frame.data == 2) mPageLiveFieldInput_IN1.setValueMIC("Mic35");
                            else if(frame.data == 3) mPageLiveFieldInput_IN1.setValueMIC("MicHP");
                            else mPageLiveFieldInput_IN1.setValueMIC("Mic1");
                            break;
                        case PageTyro:
                            if(frame.data == 1) mPageTyroFieldTop_IN1.setValueMIC("Mic1");
                            else if(frame.data == 2) mPageTyroFieldTop_IN1.setValueMIC("Mic35");
                            else if(frame.data == 3) mPageTyroFieldTop_IN1.setValueMIC("MicHP");
                            else mPageTyroFieldTop_IN1.setValueMIC("Mic1");
                            break;
                        case PageProf:
                            if(frame.data == 1) mPageProfFieldInput_IN1.setValueMIC("Mic1");
                            else if(frame.data == 2) mPageProfFieldInput_IN1.setValueMIC("Mic35");
                            else if(frame.data == 3) mPageProfFieldInput_IN1.setValueMIC("MicHP");
                            else mPageProfFieldInput_IN1.setValueMIC("Mic1");
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_48v:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN1.setValue48V(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN1.setValue48V(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN1.setValue48V(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN1.setValueGAIN(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN1.setValueGAIN(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN1.setValueGAIN(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN1.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN1.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN1.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_insert:
                    mPageLiveFieldInput_IN1.setOverlay(!frame.data);
                    mPageProfFieldInput_IN1.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_2:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN2.setVolumeMeter(frame.data);
                            mPageLiveWidgetDucking.setVolumeMeterRight(frame.data);
                            mPageLiveFieldMixer_IN12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "IN 2") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "IN 2") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "IN 2") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "IN 2") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "IN 1+2") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN2.setVolumeMeter(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN2.setVolumeMeter(frame.data);
                            mPageProfFieldMixer_IN12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "IN 2") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "IN 2") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "IN 2") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "IN 2") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "IN 1+2") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    mWidgetEQ_IN2.setVolumeMeterInputLeft(frame.data);
                    break;
                case DeviceM62::Ia_48v:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN2.setValue48V(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN2.setValue48V(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN2.setValue48V(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN2.setValueGAIN(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN2.setValueGAIN(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN2.setValueGAIN(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_IN2.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_IN2.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_IN2.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_insert:
                    mPageLiveFieldInput_IN2.setOverlay(!frame.data);
                    mPageProfFieldInput_IN2.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_3:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_AUX.setVolumeMeterLeft(frame.data);
                            mPageLiveFieldMixer_AUX.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_AUX.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_AUX.setVolumeMeterLeft(frame.data);
                            mPageProfFieldMixer_AUX.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "AUX") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "AUX") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "AUX") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "AUX") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_AUX.setValueGAIN(DeviceM62::VolumeToGain_AUX(frame.data));
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_AUX.setValueGAIN(DeviceM62::VolumeToGain_AUX(frame.data));
                            break;
                        case PageProf:
                            mPageProfFieldInput_AUX.setValueGAIN(DeviceM62::VolumeToGain_AUX(frame.data));
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_AUX.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_AUX.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_AUX.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_insert:
                    mPageLiveFieldInput_AUX.setOverlay(!frame.data);
                    mPageProfFieldInput_AUX.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_4:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_AUX.setVolumeMeterRight(frame.data);
                            mPageLiveFieldMixer_AUX.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "AUX") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_AUX.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_AUX.setVolumeMeterRight(frame.data);
                            mPageProfFieldMixer_AUX.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "AUX") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "AUX") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "AUX") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "AUX") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_5:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_BT.setVolumeMeterLeft(frame.data);
                            mPageLiveFieldMixer_BT.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "BT") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "BT") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "BT") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "BT") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_BT.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_BT.setVolumeMeterLeft(frame.data);
                            mPageProfFieldMixer_BT.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "BT") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "BT") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "BT") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "BT") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_BT.setValueGAIN(DeviceM62::VolumeToGain_BT(frame.data));
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_BT.setValueGAIN(DeviceM62::VolumeToGain_BT(frame.data));
                            break;
                        case PageProf:
                            mPageProfFieldInput_BT.setValueGAIN(DeviceM62::VolumeToGain_BT(frame.data));
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_BT.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_BT.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_BT.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_insert:
                    mPageLiveFieldInput_BT.setOverlay(!frame.data);
                    mPageProfFieldInput_BT.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_6:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_BT.setVolumeMeterRight(frame.data);
                            mPageLiveFieldMixer_BT.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "BT") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "BT") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "BT") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "BT") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_BT.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_BT.setVolumeMeterRight(frame.data);
                            mPageProfFieldMixer_BT.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "BT") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "BT") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "BT") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "BT") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_7:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_OTGIN.setVolumeMeterLeft(frame.data);
                            mPageLiveFieldMixer_OTGIN.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_OTGIN.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_OTGIN.setVolumeMeterLeft(frame.data);
                            mPageProfFieldMixer_OTGIN.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_OTGIN.setValueGAIN(DeviceM62::VolumeToGain_OTGIN(frame.data));
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_OTGIN.setValueGAIN(DeviceM62::VolumeToGain_OTGIN(frame.data));
                            break;
                        case PageProf:
                            mPageProfFieldInput_OTGIN.setValueGAIN(DeviceM62::VolumeToGain_OTGIN(frame.data));
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_OTGIN.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_OTGIN.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_OTGIN.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ia_insert:
                    mPageLiveFieldInput_OTGIN.setOverlay(!frame.data);
                    mPageProfFieldInput_OTGIN.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ic_ch_8:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ia_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldInput_OTGIN.setVolumeMeterRight(frame.data);
                            mPageLiveFieldMixer_OTGIN.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "OTG IN") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_OTGIN.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldInput_OTGIN.setVolumeMeterRight(frame.data);
                            mPageProfFieldMixer_OTGIN.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "OTG IN") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdM(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Mc_mixer_al:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_LB12.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_ar:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix A") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_LB12.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix A") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_bl:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_br:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix B") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix B") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_cl:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_cr:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix C") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix C") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_dl:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_dr:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix D") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix D") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_el:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_mixer_er:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_mixer_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Mix E") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Mix E") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Mc_vu:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ma_ch_1:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_PB12.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_2:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 1/2") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_PB12.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 1/2") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_3:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_PB34.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_4:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 3/4") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldTop_PB34.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 3/4") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_5:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_6:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 5/6") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 5/6") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_7:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB78.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB78.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_8:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB78.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 7/8") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB78.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 7/8") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_17:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB910.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB910.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB12.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB34.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB56.setVolumeMeterLeft(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB78.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ma_ch_18:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldMixer_PB910.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "Playback 9/10") mPageLiveFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            mPageProfFieldMixer_PB910.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB12.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB12.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB34.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB34.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB56.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB56.setVolumeMeterRight(frame.data);
                            if(mPageProfFieldLoopback_LB78.getAudioSource() == "Playback 9/10") mPageProfFieldLoopback_LB78.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdE(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Ec_integration:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ea_integration_nc_type:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(frame.data == 0) mPageLiveFieldEffect_Integration.setValueNoiseReductionType("Bypass");
                            else if(frame.data == 15) mPageLiveFieldEffect_Integration.setValueNoiseReductionType("NC1");
                            else if(frame.data == 30) mPageLiveFieldEffect_Integration.setValueNoiseReductionType("NC2");
                            else if(frame.data == 45) mPageLiveFieldEffect_Integration.setValueNoiseReductionType("NC3");
                            else mPageLiveFieldEffect_Integration.setValueNoiseReductionType("Bypass");
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ea_integration_reverb_drywet:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldEffect_Integration.setValueDryWet(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ea_integration_reverb_type:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            if(frame.data == 1) mPageLiveFieldEffect_Integration.setReverbType("Bypass");
                            else if(frame.data == 2) mPageLiveFieldEffect_Integration.setReverbType("STUDIO");
                            else if(frame.data == 3) mPageLiveFieldEffect_Integration.setReverbType("LIVE");
                            else if(frame.data == 4) mPageLiveFieldEffect_Integration.setReverbType("HALL");
                            else mPageLiveFieldEffect_Integration.setReverbType("Bypass");
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ea_integration_vu_in:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldEffect_Integration.setLeftVolumeMeter(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Ea_integration_vu_out:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldEffect_Integration.setRightVolumeMeter(frame.data);
                            mPageLiveFieldMixer_FX.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB12.getAudioSource() == "FX") mPageLiveFieldLoopback_LB12.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB34.getAudioSource() == "FX") mPageLiveFieldLoopback_LB34.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB56.getAudioSource() == "FX") mPageLiveFieldLoopback_LB56.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            if(mPageLiveFieldLoopback_LB78.getAudioSource() == "FX") mPageLiveFieldLoopback_LB78.setVolumeMeterLeft(frame.data).setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Ec_ducking:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ea_ducking_DuckingSwitch:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveWidgetDucking.setButtonOFFChecked(frame.data, false);
                            break;
                        case PageTyro:
                            break;
                        case PageProf:
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdL(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Lc_ch_1:
            break;
        case DeviceM62::Lc_ch_2:
            break;
        case DeviceM62::Lc_ch_3:
            break;
        case DeviceM62::Lc_ch_4:
            break;
        case DeviceM62::Lc_ch_5:
            break;
        case DeviceM62::Lc_ch_6:
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdO(DeviceType1::FrameInfo& frame)
{
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Oc_ch_1:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Oa_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_OTGOUT.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_OTGOUT.setVolumeMeterLeft(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Oc_ch_2:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Oa_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_OTGOUT.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_OTGOUT.setVolumeMeterRight(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Oa_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_OTGOUT.setValueGAIN(DeviceM62::VolumeToGain_OTGOUT(frame.data));
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setValueGAIN(DeviceM62::VolumeToGain_OTGOUT(frame.data));
                            break;
                        case PageProf:
                            mPageProfFieldOutput_OTGOUT.setValueGAIN(DeviceM62::VolumeToGain_OTGOUT(frame.data));
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Oa_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_OTGOUT.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_OTGOUT.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_OTGOUT.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Oa_insert:
                    mPageLiveFieldInput_OTGIN.setOverlay(!frame.data);
                    mPageProfFieldInput_OTGIN.setOverlay(!frame.data);
                    mPageLiveFieldOutput_OTGOUT.setOverlay(!frame.data);
                    mPageProfFieldOutput_OTGOUT.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Oc_ch_3:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Oa_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_HP.setVolumeMeterLeft(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setVolumeMeterLeft(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_HP.setVolumeMeterLeftOrigin(frame.data);
                            break;
                        default:
                            break;
                    }
                    mWidgetEQ_HP.setVolumeMeterInputLeft(frame.data);
                    break;
                default:
                    break;
            }
            break;
        case DeviceM62::Oc_ch_4:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Oa_vu:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_HP.setVolumeMeterRight(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setVolumeMeterRight(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_HP.setVolumeMeterRightOrigin(frame.data);
                            break;
                        default:
                            break;
                    }
                    mWidgetEQ_HP.setVolumeMeterInputRight(frame.data);
                    break;
                case DeviceM62::Oa_gain:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_HP.setValueGAIN(DeviceM62::VolumeToGain_HP(frame.data));
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setValueGAIN(DeviceM62::VolumeToGain_HP(frame.data));
                            break;
                        case PageProf:
                            mPageProfFieldOutput_HP.setValueGAIN(DeviceM62::VolumeToGain_HP(frame.data));
                            break;
                        default:
                            break;
                    }
                    mWidgetEQ_HP.setGainOutputLeft(frame.data);
                    break;
                case DeviceM62::Oa_mute:
                    switch(mCurrentPage)
                    {
                        case PageLive:
                            mPageLiveFieldOutput_HP.setValueMUTE(frame.data);
                            break;
                        case PageTyro:
                            mPageTyroFieldBottom_HP.setValueMUTE(frame.data);
                            break;
                        case PageProf:
                            mPageProfFieldOutput_HP.setValueMUTE(frame.data);
                            break;
                        default:
                            break;
                    }
                    break;
                case DeviceM62::Oa_insert:
                    mPageLiveFieldOutput_HP.setOverlay(!frame.data);
                    mPageProfFieldOutput_HP.setOverlay(!frame.data);
                    break;
                default:
                    break;
            }
            break;
        default:
            break;
    }
}
void MainWindow_M62::doNewFrameReceived_cmdIEQ(DeviceType1::FrameInfo& frame)
{
    QString band, property, value, channel;
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Ec_segment_1:
            band = "1";
            break;
        case DeviceM62::Ec_segment_2:
            band = "2";
            break;
        case DeviceM62::Ec_segment_3:
            band = "3";
            break;
        case DeviceM62::Ec_segment_4:
            band = "4";
            break;
        case DeviceM62::Ec_segment_5:
            band = "5";
            break;
        case DeviceM62::Ec_segment_6:
            band = "6";
            break;
        case DeviceM62::Ec_segment_7:
            band = "7";
            break;
        case DeviceM62::Ec_segment_8:
            band = "8";
            break;
        case DeviceM62::Ec_segment_9:
            band = "9";
            break;
        case DeviceM62::Ec_segment_10:
            band = "10";
            break;
        case DeviceM62::Ec_parameter:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ea_VU_channel_1:
                    mWidgetEQ_IN1.setVolumeMeterOutputLeft(frame.data);
                    break;
                case DeviceM62::Ea_VU_channel_2:
                    mWidgetEQ_IN2.setVolumeMeterOutputLeft(frame.data);
                    break;
                case DeviceM62::Ea_Output_gain_1:
                    mWidgetEQ_IN1.setGainOutputLeft(USBAHandle.GainToLog(frame.data));
                    break;
                case DeviceM62::Ea_Output_gain_2:
                    mWidgetEQ_IN2.setGainOutputLeft(USBAHandle.GainToLog(frame.data));
                    break;
                case DeviceM62::Ea_Master_switch_1:
                    mWidgetEQ_IN1.setSwitchStatus(frame.data);
                    mPageProfFieldInput_IN1.setValueEQ(frame.data);
                    mPageLiveFieldInput_IN1.setValueEQ(frame.data);
                    break;
                case DeviceM62::Ea_Master_switch_2:
                    mWidgetEQ_IN2.setSwitchStatus(frame.data);
                    mPageProfFieldInput_IN2.setValueEQ(frame.data);
                    mPageLiveFieldInput_IN2.setValueEQ(frame.data);
                    break;
                default:
                    break;
            }
            return;
        default:
            break;
    }
    switch(frame.cmd & 0x00ff)
    {
        case DeviceM62::Ea_EnableState_l:
            property = "Switch";
            value = QString::number(frame.data);
            channel = "L";
            break;
        case DeviceM62::Ea_EnableState_r:
            property = "Switch";
            value = QString::number(frame.data);
            channel = "R";
            break;
        case DeviceM62::Ea_FilterType_l:
            property = "Type";
            value = QString::number(frame.data - 1);
            channel = "L";
            break;
        case DeviceM62::Ea_FilterType_r:
            property = "Type";
            value = QString::number(frame.data - 1);
            channel = "R";
            break;
        case DeviceM62::Ea_gain_l:
            property = "Gain";
            value = QString::number(frame.data / 10.0);
            channel = "L";
            break;
        case DeviceM62::Ea_gain_r:
            property = "Gain";
            value = QString::number(frame.data / 10.0);
            channel = "R";
            break;
        case DeviceM62::Ea_Frequency_l:
            property = "Freq";
            value = QString::number(frame.data);
            channel = "L";
            break;
        case DeviceM62::Ea_Frequency_r:
            property = "Freq";
            value = QString::number(frame.data);
            channel = "R";
            break;
        case DeviceM62::Ea_QualityValue_l:
            property = "Q";
            value = QString::number(frame.data / 10000.0);
            channel = "L";
            break;
        case DeviceM62::Ea_QualityValue_r:
            property = "Q";
            value = QString::number(frame.data / 10000.0);
            channel = "R";
            break;
        default:
            break;
    }
    if(channel == "L") mWidgetEQ_IN1.setEqualizerData(band, property, value);
    else if(channel == "R") mWidgetEQ_IN2.setEqualizerData(band, property, value);
}
void MainWindow_M62::doNewFrameReceived_cmdOEQ(DeviceType1::FrameInfo& frame)
{
    QString band, property, value, channel;
    switch(frame.cmd & 0x0f00)
    {
        case DeviceM62::Ec_segment_1:
            band = "1";
            break;
        case DeviceM62::Ec_segment_2:
            band = "2";
            break;
        case DeviceM62::Ec_segment_3:
            band = "3";
            break;
        case DeviceM62::Ec_segment_4:
            band = "4";
            break;
        case DeviceM62::Ec_segment_5:
            band = "5";
            break;
        case DeviceM62::Ec_segment_6:
            band = "6";
            break;
        case DeviceM62::Ec_segment_7:
            band = "7";
            break;
        case DeviceM62::Ec_segment_8:
            band = "8";
            break;
        case DeviceM62::Ec_segment_9:
            band = "9";
            break;
        case DeviceM62::Ec_segment_10:
            band = "10";
            break;
        case DeviceM62::Ec_parameter:
            switch(frame.cmd & 0x00ff)
            {
                case DeviceM62::Ea_VU_channel_1:
                    mWidgetEQ_HP.setVolumeMeterOutputLeft(frame.data);
                    mPageProfFieldOutput_HP.setVolumeMeterLeftGained(frame.data);
                    break;
                case DeviceM62::Ea_VU_channel_2:
                    mWidgetEQ_HP.setVolumeMeterOutputRight(frame.data);
                    mPageProfFieldOutput_HP.setVolumeMeterRightGained(frame.data);
                    break;
                case DeviceM62::Ea_Master_switch_1:
                    mWidgetEQ_HP.setSwitchStatus(frame.data);
                    mPageProfFieldOutput_HP.setValueEQ(frame.data);
                    break;
                default:
                    break;
            }
            return;
        default:
            break;
    }
    switch(frame.cmd & 0x00ff)
    {
        case DeviceM62::Ea_EnableState_l:
            property = "Switch";
            value = QString::number(frame.data);
            channel = "L";
            break;
        case DeviceM62::Ea_EnableState_r:
            property = "Switch";
            value = QString::number(frame.data);
            channel = "R";
            break;
        case DeviceM62::Ea_FilterType_l:
            property = "Type";
            value = QString::number(frame.data - 1);
            channel = "L";
            break;
        case DeviceM62::Ea_FilterType_r:
            property = "Type";
            value = QString::number(frame.data - 1);
            channel = "R";
            break;
        case DeviceM62::Ea_gain_l:
            property = "Gain";
            value = QString::number(frame.data / 10.0);
            channel = "L";
            break;
        case DeviceM62::Ea_gain_r:
            property = "Gain";
            value = QString::number(frame.data / 10.0);
            channel = "R";
            break;
        case DeviceM62::Ea_Frequency_l:
            property = "Freq";
            value = QString::number(frame.data);
            channel = "L";
            break;
        case DeviceM62::Ea_Frequency_r:
            property = "Freq";
            value = QString::number(frame.data);
            channel = "R";
            break;
        case DeviceM62::Ea_QualityValue_l:
            property = "Q";
            value = QString::number(frame.data / 10000.0);
            channel = "L";
            break;
        case DeviceM62::Ea_QualityValue_r:
            property = "Q";
            value = QString::number(frame.data / 10000.0);
            channel = "R";
            break;
        default:
            break;
    }
    if(channel == "L") mWidgetEQ_HP.setEqualizerData(band, property, value);
    // else if(channel == "R") ;
}
void MainWindow_M62::doNewFrameReceived_cmdT(DeviceType1::FrameInfo& frame)
{
    Q_UNUSED(frame);
}


// setup
void MainWindow_M62::setupMainWindowWidget()
{
    initMainWindowWidget();
}
void MainWindow_M62::setupPageMble()
{
    initPageMbleFieldHead();
    initPageMbleFieldInput();
    initPageMbleFieldEffect();
    initPageMbleFieldOutput();
    initPageMbleWidget();
}
void MainWindow_M62::setupPageLive()
{
    initPageLiveFieldHead();
    initPageLiveFieldInput();
    initPageLiveFieldMixer();
    initPageLiveFieldEffect();
    initPageLiveFieldLoopback();
    initPageLiveFieldOutput();
    initPageLiveWidget();
}
void MainWindow_M62::setupPageTyro()
{
    initPageTyroFieldTop();
    initPageTyroFieldBottom();
}
void MainWindow_M62::setupPageProf()
{
    initPageProfFieldHead();
    initPageProfFieldInput();
    initPageProfFieldMixer();
    initPageProfFieldLoopback();
    initPageProfFieldOutput();
    initPageProfWidget();
}


// adjust
void MainWindow_M62::adjustGeometryPageFcty()
{
    ui->statusBar->hide();
    setFixedSize(1100 * QSettings().value("ScaleFactor").toFloat(), 645 * QSettings().value("ScaleFactor").toFloat());
}
void MainWindow_M62::adjustGeometryPageMble()
{
    ui->statusBar->hide();
    // setFixedSize(1100 * QSettings().value("ScaleFactor").toFloat(), 645 * QSettings().value("ScaleFactor").toFloat());
}
void MainWindow_M62::adjustGeometryPageLive()
{
    ui->statusBar->show();
    setFixedHeight(645 * QSettings().value("ScaleFactor").toFloat());
    setMinimumWidth(1100 * QSettings().value("ScaleFactor").toFloat());
    if(!QSettings().contains("M62/PageLiveFactor1"))
    {
        ui->PageLiveSplitterIE_MLO->setSizes({460, 800});
        QSettings().setValue("M62/PageLiveFactor1", ui->PageLiveSplitterIE_MLO->saveState());
    }
    ui->PageLiveSplitterIE_MLO->restoreState(QSettings().value("M62/PageLiveFactor1").toByteArray());
    if(!QSettings().contains("M62/PageLiveFactor2"))
    {
        ui->PageLiveSplitterL_O->setSizes({200, 200});
        QSettings().setValue("M62/PageLiveFactor2", ui->PageLiveSplitterL_O->saveState());
    }
    ui->PageLiveSplitterL_O->restoreState(QSettings().value("M62/PageLiveFactor2").toByteArray());
    if(!QSettings().contains("M62/PageLiveWidth"))
    {
        QSettings().setValue("M62/PageLiveWidth", 1100 * QSettings().value("ScaleFactor").toFloat());
    }
    resize(QSettings().value("M62/PageLiveWidth").toInt(), 645 * QSettings().value("ScaleFactor").toFloat());
}
void MainWindow_M62::adjustGeometryPageTyro()
{
    ui->statusBar->show();
}
void MainWindow_M62::adjustGeometryPageProf()
{
    ui->statusBar->show();
    setFixedHeight(645 * QSettings().value("ScaleFactor").toFloat());
    setMinimumWidth(1100 * QSettings().value("ScaleFactor").toFloat());
    if(!QSettings().contains("M62/PageProfFactor1"))
    {
        ui->PageProfSplitterIL_MO->setSizes({460, 800});
        QSettings().setValue("M62/PageProfFactor1", ui->PageProfSplitterIL_MO->saveState());
    }
    ui->PageProfSplitterIL_MO->restoreState(QSettings().value("M62/PageProfFactor1").toByteArray());
    if(!QSettings().contains("M62/PageProfWidth"))
    {
        QSettings().setValue("M62/PageProfWidth", 1100 * QSettings().value("ScaleFactor").toFloat());
    }
    resize(QSettings().value("M62/PageProfWidth").toInt(), 645 * QSettings().value("ScaleFactor").toFloat());
}


// setter & getter
void MainWindow_M62::setPageToActive(PageID id)
{
    mCurrentPage = id;
    ui->stackedWidget->setCurrentIndex(mCurrentPage);
    WKSPHandle.removeObserverAll();
    switch(id)
    {
        case PageUpgd:

            break;
        case PageFcty:
            adjustGeometryPageFcty();
            break;
        case PageMble:
            WKSPHandle.addObserverList(mPageMbleWorkspaceObserverList);
            WKSPHandle.assignWorkspace("M62", "./Workspace/M62/Mble", "M62_Mble_");
            ui->PageMbleFieldHead->modifyWorkspaceList(WKSPHandle.getWorkspaceList(), QSettings().value("M62/M62_Mble_Workspace").toString());
            adjustGeometryPageMble();
            mSettingsDevice.setDisplayMode("Mobile mode");
            break;
        case PageLive:
            QSettings().setValue("M62/PageID", mCurrentPage);
            // Workspace
            WKSPHandle.addObserverList(mPageLiveWorkspaceObserverList);
            WKSPHandle.assignWorkspace("M62", "./Workspace/M62/Live", "M62_Live_");
            ui->PageLiveFieldHead->modifyWorkspaceList(WKSPHandle.getWorkspaceList(), QSettings().value("M62/M62_Live_Workspace").toString());
            adjustGeometryPageLive();
            mSettingsDevice.setDisplayMode("Live streaming mode");
            break;
        case PageTyro:
            QSettings().setValue("M62/PageID", mCurrentPage);
            // Mixer_AUX
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixA_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixA_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixB_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixB_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixC_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_AUX", "MixC_GainMRCR", "33554432");
            // Mixer_BT
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixA_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixA_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixB_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixB_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixC_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_BT", "MixC_GainMRCR", "33554432");
            // Mixer_OTGIN
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixA_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixA_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixB_GainMLCL", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixB_GainMRCR", "33554432");
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixC_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_OTGIN", "MixC_GainMRCR", "0");
            // Mixer_PB56
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixA_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixA_GainMRCR", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixB_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixB_GainMRCR", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixC_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB56", "MixC_GainMRCR", "0");
            // Mixer_PB78
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixA_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixA_GainMRCR", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixB_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixB_GainMRCR", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixC_GainMLCL", "0");
            mDevice.in_fieldMixer_attributeChanged("Mixer_PB78", "MixC_GainMRCR", "0");
            // Effect_Integration
            mDevice.in_fieldEffect_attributeChanged("Effect_Integration", "GAIN", "0");
            mDevice.in_fieldEffect_attributeChanged("Effect_Integration", "MUTE", "0");
            // Effect_Integration
            mDevice.in_fieldEffect_attributeChanged("Effect_Integration", "MUTE", "0");
            // Loopback_LB12
            mDevice.in_fieldLoopback_attributeChanged("Loopback_LB12", "AudioSource", "Mix A");
            // Loopback_LB34
            mDevice.in_fieldLoopback_attributeChanged("Loopback_LB34", "GAIN", "-90");
            // Loopback_LB56
            mDevice.in_fieldLoopback_attributeChanged("Loopback_LB56", "GAIN", "-90");
            // Output_HP
            mDevice.in_fieldOutput_attributeChanged("Output_HP", "AudioSource", "Mix B");
            // Output_OTGOUT
            mDevice.in_fieldOutput_attributeChanged("Output_OTGOUT", "AudioSource", "Mix C");
            // Workspace
            WKSPHandle.addObserverList(mPageTyroWorkspaceObserverList);
            WKSPHandle.assignWorkspace("M62", "./Workspace/M62/Tyro", "M62_Tyro_");
            if(!WKSPHandle.isValid("configFile"))
            {
                WKSPHandle.createWorkspace("configFile");
            }
            WKSPHandle.modifyWorkspace("configFile");
            mSettingsDevice.setDisplayMode("Easy mode");
            break;
        case PageProf:
            // Workspace
            WKSPHandle.addObserverList(mPageProfWorkspaceObserverList);
            WKSPHandle.assignWorkspace("M62", "./Workspace/M62/Prof", "M62_Prof_");
            ui->PageProfFieldHead->modifyWorkspaceList(WKSPHandle.getWorkspaceList(), QSettings().value("M62/M62_Prof_Workspace").toString());
            adjustGeometryPageProf();
            mSettingsDevice.setDisplayMode("Pro audio mode");
            break;
        default:
            break;
    }
}

