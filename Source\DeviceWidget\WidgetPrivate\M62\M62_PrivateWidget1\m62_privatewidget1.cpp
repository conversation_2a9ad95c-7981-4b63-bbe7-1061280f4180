#include "m62_privatewidget1.h"
#include "ui_m62_privatewidget1.h"
#include <QPainter>
#include "globalfont.h"
#include <float.h>

M62_PrivateWidget1::M62_PrivateWidget1(QWidget *parent, const QString& name)
    : FramelessWindow(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::M62_PrivateWidget1)
    , mWidget(new QWidget)
{
    ui->setupUi(mWidget);
    setWindowFlags(windowFlags() | Qt::SplashScreen);
    setCentralWidget(mWidget);
    setRightBottomDraggable();
    initConnect();

    setMinimumSize(485, 295);
    ui->widgetDials->setObjectName("widgetDials");
    ui->DialAttack->setColorBG(Qt::transparent);
    ui->DialAttack->showCircle(false);
    ui->DialReduction->setColorBG(Qt::transparent);
    ui->DialReduction->showCircle(false);
    ui->DialRelease->setColorBG(Qt::transparent);
    ui->DialRelease->showCircle(false);
    ui->DialThreshold->setColorBG(Qt::transparent);
    ui->DialThreshold->showCircle(false);

    ui->widgetDockingMap->setStyleSheet("QWidget{background:rgb(22, 22, 22);border-radius:10px}");
    ui->widget->setStyleSheet("background:transparent;color:rgba(222, 222, 222, 1)");
    ui->widgetD->setStyleSheet("background:transparent;color:rgb(170, 170, 170);");
    ui->labelTitle_2->setStyleSheet(QString("background:rgb(22, 22, 22);"
            "   border-top-left-radius: 10px;"
            "   border-top-right-radius: 10px;"
            "   border-bottom-left-radius: 0px;"
            "   border-bottom-right-radius: 0px;"
            "   border-bottom: 1px solid rgb(31,31,31);"
            "   color:rgb(170, 170, 170);"));
    ui->widgetDials->setStyleSheet("QWidget{background:rgb(22, 22, 22);border-radius:10px}");
    ui->DialThresholdT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialAttackT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialReductionT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialReleaseT->setStyleSheet("color:rgba(222, 222, 222, 1)");

    reset();
    ui->DialThreshold->setStep(1);
    ui->DialAttack->setStep(50);
    ui->DialAttack->setStepWheel(10);
    ui->DialReduction->setStep(1);
    ui->DialRelease->setStep(50);
    ui->DialRelease->setStepWheel(10);
    setThresholdRange(-60, -1);
    setThresholdDefaultValue(-30);
    setThresholdLabelL("-60dB");
    setThresholdLabelR("-1dB");
    setAttackRange(0, 1000);
    setAttackDefaultValue(200);
    setAttackLabelL("0ms");
    setAttackLabelR("1000ms");
    setReductionRange(1, 30);
    setReductionDefaultValue(16);
    setReductionLabelL("1dB");
    setReductionLabelR("30dB");
    setReleaseRange(10, 2000);
    setReleaseDefaultValue(1000);
    setReleaseLabelL("10ms");
    setReleaseLabelR("2000ms");
}

M62_PrivateWidget1::~M62_PrivateWidget1()
{
    delete ui;
}

void M62_PrivateWidget1::setName(const QString &name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
}

void M62_PrivateWidget1::setWidthHeight(bool isCenter)
{
    QRect rect = APPSHandle.getMainWindow()->geometry();
    int h = rect.height() * 0.6;
    int w = h * minimumWidth() / minimumHeight();
    resize(w, h);
    if(isCenter){
        move(QPoint(rect.x(),rect.y()) + QPoint((rect.width() - w) / 2, (rect.height() - h) / 2));
    }
}

void M62_PrivateWidget1::show()
{
    setWidthHeight(true);
    setVisible(true);
}

void M62_PrivateWidget1::setFont(const QFont &font)
{
    FramelessWindow::setFont(font);
    setAllChildFont(this, font);
    ui->input->setFont(font);
}

void M62_PrivateWidget1::setINName(const QString &str)
{
    ui->input->setName(str);
}

void M62_PrivateWidget1::setINChannelName(const QString &str)
{
    ui->input->setChannelName(str,false);
}

void M62_PrivateWidget1::setVolumeMeterLeft(int value)
{
    ui->input->setVolumeMeterLeft(value);
}

void M62_PrivateWidget1::setVolumeMeterRight(int value)
{
    ui->input->setVolumeMeterRight(value);
}

float M62_PrivateWidget1::getThresholdDefaultValue()
{
    return ui->DialThreshold->getDefault();
}

void M62_PrivateWidget1::setThresholdDefaultValue(float value)
{
    ui->DialThreshold->setDefault(value);
}

void M62_PrivateWidget1::setThresholdValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialThreshold);
        ui->DialThreshold->setValue(value);
    }else{
        ui->DialThreshold->setValue(value);
    }
}

void M62_PrivateWidget1::setThresholdRange(float min, float max)
{
    ui->DialThreshold->setRange(min, max);
}

void M62_PrivateWidget1::setThresholdLabelL(const QString &text)
{
    ui->DialThresholdL->setText(text);
}

void M62_PrivateWidget1::setThresholdLabelR(const QString &text)
{
    ui->DialThresholdR->setText(text);
}

float M62_PrivateWidget1::getAttackDefaultValue()
{
    return ui->DialAttack->getDefault();
}

void M62_PrivateWidget1::setAttackDefaultValue(float value)
{
    ui->DialAttack->setDefault(value);
}

void M62_PrivateWidget1::setAttackValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialAttack);
        ui->DialAttack->setValue(value);
    }else{
        ui->DialAttack->setValue(value);
    }
}

void M62_PrivateWidget1::setAttackRange(float min, float max)
{
    ui->DialAttack->setRange(min, max);
}

void M62_PrivateWidget1::setAttackLabelL(const QString &text)
{
    ui->DialAttackL->setText(text);
}

void M62_PrivateWidget1::setAttackLabelR(const QString &text)
{
    ui->DialAttackR->setText(text);
}

float M62_PrivateWidget1::getReductionDefaultValue()
{
    return ui->DialReduction->getDefault();
}

void M62_PrivateWidget1::setReductionDefaultValue(float value)
{
    ui->DialReduction->setDefault(value);
}

void M62_PrivateWidget1::setReductionValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialReduction);
        ui->DialReduction->setValue(value);
    }else{
        ui->DialReduction->setValue(value);
    }
}

void M62_PrivateWidget1::setReductionRange(float min, float max)
{
    ui->DialReduction->setRange(min, max);
}

void M62_PrivateWidget1::setReductionLabelL(const QString &text)
{
    ui->DialReductionL->setText(text);
}

void M62_PrivateWidget1::setReductionLabelR(const QString &text)
{
    ui->DialReductionR->setText(text);
}

float M62_PrivateWidget1::getReleaseDefaultValue()
{
    return ui->DialRelease->getDefault();
}

void M62_PrivateWidget1::setReleaseDefaultValue(float value)
{
    ui->DialRelease->setDefault(value);
}

void M62_PrivateWidget1::setReleaseValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialRelease);
        ui->DialRelease->setValue(value);
    }else{
        ui->DialRelease->setValue(value);
    }
}

void M62_PrivateWidget1::setReleaseRange(float min, float max)
{
    ui->DialRelease->setRange(min, max);
}

void M62_PrivateWidget1::setReleaseLabelL(const QString &text)
{
    ui->DialReleaseL->setText(text);
}

void M62_PrivateWidget1::setReleaseLabelR(const QString &text)
{
    ui->DialReleaseR->setText(text);
}

void M62_PrivateWidget1::setButtonOFFChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->buttonOFF);
        ui->buttonOFF->setChecked(isChecked);
    }else{
        ui->buttonOFF->setChecked(isChecked);
    }
    mOFF = isChecked;
    WorkspaceObserver::setValue("DuckingSwitch", isChecked?"true":"false");
    setOFFButtonText(isChecked);
    saveAttribute("DuckingSwitch", QString::number(isChecked));
    sendSigWorkState(1);
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setIN1AUXChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1AUX);
        ui->IN1AUX->setChecked(isChecked);
    }else{
        ui->IN1AUX->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setIN2AUXChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2AUX);
        ui->IN2AUX->setChecked(isChecked);
    }else{
        ui->IN2AUX->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setIN1BTChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1BT);
        ui->IN1BT->setChecked(isChecked);
    }else{
        ui->IN1BT->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setIN2BTChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2BT);
        ui->IN2BT->setChecked(isChecked);
    }else{
        ui->IN2BT->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setIN1OTGChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1OTG);
        ui->IN1OTG->setChecked(isChecked);
    }else{
        ui->IN1OTG->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setIN2OTGChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2OTG);
        ui->IN2OTG->setChecked(isChecked);
    }else{
        ui->IN2OTG->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setplayback1_2IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback1_2IN1);
        ui->playback1_2IN1->setChecked(isChecked);
    }else{
        ui->playback1_2IN1->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setplayback1_2IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback1_2IN2);
        ui->playback1_2IN2->setChecked(isChecked);
    }else{
        ui->playback1_2IN2->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setplayback3_4IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback3_4IN1);
        ui->playback3_4IN1->setChecked(isChecked);
    }else{
        ui->playback3_4IN1->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setplayback3_4IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback3_4IN2);
        ui->playback3_4IN2->setChecked(isChecked);
    }else{
        ui->playback3_4IN2->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setplayback5_6IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback5_6IN1);
        ui->playback5_6IN1->setChecked(isChecked);
    }else{
        ui->playback5_6IN1->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setplayback5_6IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback5_6IN2);
        ui->playback5_6IN2->setChecked(isChecked);
    }else{
        ui->playback5_6IN2->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setplayback7_8IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback7_8IN1);
        ui->playback7_8IN1->setChecked(isChecked);
    }else{
        ui->playback7_8IN1->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setplayback7_8IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback7_8IN2);
        ui->playback7_8IN2->setChecked(isChecked);
    }else{
        ui->playback7_8IN2->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::setplayback9_10IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback9_10IN1);
        ui->playback9_10IN1->setChecked(isChecked);
    }else{
        ui->playback9_10IN1->setChecked(isChecked);
    }
    sendSigWorkState(1);
}

void M62_PrivateWidget1::setplayback9_10IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback9_10IN2);
        ui->playback9_10IN2->setChecked(isChecked);
    }else{
        ui->playback9_10IN2->setChecked(isChecked);
    }
    sendSigWorkState(2);
}

void M62_PrivateWidget1::resizeEvent(QResizeEvent *e)
{
    FramelessWindow::resizeEvent(e);
    adjustGeometry();
}

void M62_PrivateWidget1::showEvent(QShowEvent *e)
{
    FramelessWindow::showEvent(e);
    adjustGeometry();
}

void M62_PrivateWidget1::setAllChildFont(QWidget* widget, const QFont& font)
{
    for (auto child : widget->children()) {
        QWidget *widget = qobject_cast<QWidget *>(child);
        if (widget) {
            widget->setFont(font);
            setAllChildFont(widget, font);
        }
    }
}

void M62_PrivateWidget1::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    WorkspaceObserver::setValue(attribute, value);
    updateAttribute();
    saveAttribute(attribute);
}

void M62_PrivateWidget1::setDialGeometry(QWidget *dial, QLabel *top, QLabel *left, QLabel *right)
{
    QWidget* parent = (QWidget*)dial->parent();
    int w = parent->width();
    int h = parent->height();
    float wPixelPerRatio= w / 100.0;
    float hPixelPerRatio = h / 100.0;

    QRect rect(wPixelPerRatio*20, hPixelPerRatio*20, wPixelPerRatio * 60, hPixelPerRatio * 60);
    dial->setGeometry(rect);

    QFont font = GLBFHandle.font();
    top->setGeometry(0, 0, w, hPixelPerRatio * 20);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, top->height()));
    top->setFont(font);

    left->setGeometry(0, hPixelPerRatio * 70, wPixelPerRatio*45, hPixelPerRatio * 20);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, left->height())*80/100);
    left->setFont(font);

    right->setGeometry(wPixelPerRatio*55, hPixelPerRatio * 70, wPixelPerRatio*45, hPixelPerRatio * 20);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, right->height())*80/100);
    right->setFont(font);
}

void M62_PrivateWidget1::initConnect()
{
    connect(ui->input, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetListAll_attributeChanged(QString, QString, QString)));
    connect(ui->DialThreshold, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Threshold", QString::number(value));
    });
    connect(ui->DialAttack, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Attack", QString::number(value));
    });
    connect(ui->DialReduction, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Reduction", QString::number(value));
    });
    connect(ui->DialRelease, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Release", QString::number(value));
    });
    connect(ui->buttonOFF, SIGNAL(toggled(bool)), this, SLOT(on_buttonOFF_toggled(bool)));
    connect(ui->IN1AUX, SIGNAL(toggled(bool)), this, SLOT(on_IN1AUX_toggled(bool)));
    connect(ui->IN2AUX, SIGNAL(toggled(bool)), this, SLOT(on_IN2AUX_toggled(bool)));
    connect(ui->IN1BT, SIGNAL(toggled(bool)), this, SLOT(on_IN1BT_toggled(bool)));
    connect(ui->IN2BT, SIGNAL(toggled(bool)), this, SLOT(on_IN2BT_toggled(bool)));
    connect(ui->IN1OTG, SIGNAL(toggled(bool)), this, SLOT(on_IN1OTG_toggled(bool)));
    connect(ui->IN2OTG, SIGNAL(toggled(bool)), this, SLOT(on_IN2OTG_toggled(bool)));
    connect(ui->playback1_2IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback1_2IN1_toggled(bool)));
    connect(ui->playback1_2IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback1_2IN2_toggled(bool)));
    connect(ui->playback3_4IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback3_4IN1_toggled(bool)));
    connect(ui->playback3_4IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback3_4IN2_toggled(bool)));
    connect(ui->playback5_6IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback5_6IN1_toggled(bool)));
    connect(ui->playback5_6IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback5_6IN2_toggled(bool)));
    connect(ui->playback7_8IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback7_8IN1_toggled(bool)));
    connect(ui->playback7_8IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback7_8IN2_toggled(bool)));
    connect(ui->playback9_10IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback9_10IN1_toggled(bool)));
    connect(ui->playback9_10IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback9_10IN2_toggled(bool)));
}

void M62_PrivateWidget1::adjustGeometry()
{
    auto getWHHoldRatio = [](QWidget* parent, QWidget* child, double wRatio=1.0f, double hRatio=1.0f){
        double aspectRatio = static_cast<double>(child->minimumWidth()) / child->minimumHeight();
        double widthScale = static_cast<double>(parent->width()) *wRatio / child->minimumWidth();
        double heightScale = static_cast<double>(parent->height()) *hRatio / child->minimumHeight();
        double scale = std::min(widthScale, heightScale);
        double newWidth = child->minimumWidth() * scale;
        int newHeight = static_cast<int>(newWidth / aspectRatio);
        return std::make_tuple(newWidth, newHeight);
    };

    {
        auto wh =  getWHHoldRatio(ui->widget_2, ui->input);
        int w =std::get<0>(wh);
        int h =std::get<1>(wh);
        ui->input->setGeometry((ui->widget_2->width()-w)/2,(ui->widget_2->height()-h)/2,w, h);
    }

    {
        auto wh =  getWHHoldRatio(ui->widget_3, ui->widgetDials);
        int w =std::get<0>(wh);
        int h =std::get<1>(wh);
        ui->widgetDials->setGeometry((ui->widget_3->width()-w)/2,(ui->widget_3->height()-h)/2,w, h);
    }

    {
        {
            auto wh =  getWHHoldRatio(ui->widget_4, ui->widgetDockingMap);
            int w =std::get<0>(wh);
            int h =std::get<1>(wh);
            ui->widgetDockingMap->setGeometry((ui->widget_4->width()-w)/2,(ui->widget_4->height()-h)/2,w, h);
            double hPixelPerRatio = ui->widgetDockingMap->height()/100.0;
            ui->labelTitle_2->setGeometry(0,0, ui->widgetDockingMap->width(),hPixelPerRatio*10);
            ui->widget->setGeometry(0, hPixelPerRatio*10, ui->widgetDockingMap->width(), hPixelPerRatio*90);
        }

        {
            double wPixelPerRatio = ui->widget->width()/100.0;
            double hPixelPerRatio = ui->widget->height()/100.0;
            int labelIN1X = 10*wPixelPerRatio;
            int labelIN2X = 32*wPixelPerRatio;
            int titleX = 55*wPixelPerRatio;
            int titleW = 41*wPixelPerRatio;

            auto wh =  getWHHoldRatio(ui->widget, ui->IN1AUX, (double)1/100*16, (double)1/8);
            int newWidth =std::get<0>(wh);
            int newHeight =std::get<1>(wh);
            int labelHeight = 1.2*newHeight;
            int labelYOffset = -(labelHeight-newHeight)/2;

            ui->labelIN1->setGeometry(labelIN1X, 5*hPixelPerRatio, newWidth, labelHeight);
            ui->labelIN2->setGeometry(labelIN2X, 5*hPixelPerRatio, newWidth, labelHeight);

            ui->IN1AUX->setGeometry(labelIN1X, 18*hPixelPerRatio, newWidth, newHeight);
            ui->IN2AUX->setGeometry(labelIN2X, 18*hPixelPerRatio,  newWidth, newHeight);
            ui->labelAUX->setGeometry(titleX, 18*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->IN1BT->setGeometry(labelIN1X, 28*hPixelPerRatio, newWidth, newHeight);
            ui->IN2BT->setGeometry(labelIN2X, 28*hPixelPerRatio, newWidth, newHeight);
            ui->labelBT->setGeometry(titleX, 28*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->IN1OTG->setGeometry(labelIN1X, 38*hPixelPerRatio, newWidth, newHeight);
            ui->IN2OTG->setGeometry(labelIN2X, 38*hPixelPerRatio, newWidth, newHeight);
            ui->labelOTG->setGeometry(titleX, 38*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->playback1_2IN1->setGeometry(labelIN1X, 48*hPixelPerRatio, newWidth, newHeight);
            ui->playback1_2IN2->setGeometry(labelIN2X, 48*hPixelPerRatio, newWidth, newHeight);
            ui->labelpb12->setGeometry(titleX, 48*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->playback3_4IN1->setGeometry(labelIN1X, 58*hPixelPerRatio,newWidth, newHeight);
            ui->playback3_4IN2->setGeometry(labelIN2X, 58*hPixelPerRatio, newWidth, newHeight);
            ui->labelpb34->setGeometry(titleX, 58*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->playback5_6IN1->setGeometry(labelIN1X, 68*hPixelPerRatio, newWidth, newHeight);
            ui->playback5_6IN2->setGeometry(labelIN2X, 68*hPixelPerRatio, newWidth, newHeight);
            ui->labelpb56->setGeometry(titleX, 68*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->playback7_8IN1->setGeometry(labelIN1X, 78*hPixelPerRatio, newWidth, newHeight);
            ui->playback7_8IN2->setGeometry(labelIN2X, 78*hPixelPerRatio, newWidth, newHeight);
            ui->labelpb78->setGeometry(titleX, 78*hPixelPerRatio+labelYOffset, titleW, labelHeight);

            ui->playback9_10IN1->setGeometry(labelIN1X, 88*hPixelPerRatio, newWidth, newHeight);
            ui->playback9_10IN2->setGeometry(labelIN2X, 88*hPixelPerRatio, newWidth, newHeight);
            ui->labelpb910->setGeometry(titleX, 88*hPixelPerRatio+labelYOffset, titleW, labelHeight);
        }
    }

    {
        double wPixelPerRatio = ui->widgetDials->width() / 100.0;
        double hPixelPerRatio = ui->widgetDials->height() / 100.0;
        ui->widgetD->setGeometry(0,0,ui->widgetDials->width(),hPixelPerRatio*80);
        ui->buttonOFF->setGeometry(wPixelPerRatio*35, hPixelPerRatio*80, wPixelPerRatio*30, hPixelPerRatio*9);
        double wPixelPerRatio1= ui->widgetD->width() / 100.0;
        double hPixelPerRatio1 = ui->widgetD->height() / 100.0;
        double wSpace = wPixelPerRatio1*5;
        double hSpace = hPixelPerRatio1*5;
        int w = wPixelPerRatio1*40;
        int h = hPixelPerRatio1*40;
        ui->widgetThreshold->setGeometry(wSpace,hSpace, w, h);
        ui->widgetlAttack->setGeometry(w+3*wSpace,hSpace, w, h);
        ui->widgeReduction->setGeometry(wSpace,h+3*hSpace, w, h);
        ui->widgetRelease->setGeometry(w+3*wSpace,h+3*hSpace, w, h);
        setDialGeometry(ui->DialAttack, ui->DialAttackT, ui->DialAttackL, ui->DialAttackR);
        setDialGeometry(ui->DialReduction, ui->DialReductionT, ui->DialReductionL, ui->DialReductionR);
        setDialGeometry(ui->DialRelease, ui->DialReleaseT, ui->DialReleaseL, ui->DialReleaseR);
        setDialGeometry(ui->DialThreshold, ui->DialThresholdT, ui->DialThresholdL, ui->DialThresholdR);
    }

    QFont font = ui->labelAUX->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->labelAUX->height())*0.45);
    ui->labelIN1->setFont(font);
    ui->labelIN2->setFont(font);
    ui->labelAUX->setFont(font);
    ui->labelBT->setFont(font);
    ui->labelOTG->setFont(font);
    ui->labelpb12->setFont(font);
    ui->labelpb34->setFont(font);
    ui->labelpb56->setFont(font);
    ui->labelpb78->setFont(font);
    ui->labelpb910->setFont(font);
    ui->DialAttack->setFont(font);
    ui->DialReduction->setFont(font);
    ui->DialRelease->setFont(font);
    ui->DialThreshold->setFont(font);

    font = ui->labelTitle_2->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->labelTitle_2->height())*75/100);
    ui->labelTitle_2->setFont(font);

    font = ui->buttonOFF->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->buttonOFF->height())*80/100);
    ui->buttonOFF->setFont(font);

    QString style=QString("QPushButton{color: rgba(255, 255, 255, 1);"
                            "border-radius: %1px;"
                            "background: rgba(48, 48, 48, 1);}"
                            "QPushButton:checked{background: rgba(1, 150, 65, 1);}").arg(3*(ui->buttonOFF->height()/(double)ui->buttonOFF->minimumHeight()));
    ui->buttonOFF->setStyleSheet(style);
}

void M62_PrivateWidget1::on_buttonOFF_toggled(bool checked)
{
    setOFFButtonText(checked);
    in_mWidgetListAll_attributeChanged("", "DuckingSwitch", checked?"true":"false");
}

void M62_PrivateWidget1::on_IN1AUX_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1AUX", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2AUX_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2AUX", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN1BT_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1BT", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2BT_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2BT", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN1OTG_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1OTGIN", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2OTG_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2OTGIN", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback1_2IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback1_2", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback1_2IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback1_2", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback3_4IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback3_4", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback3_4IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback3_4", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback5_6IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback5_6", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback5_6IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback5_6", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback7_8IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback7_8", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback7_8IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback7_8", checked?"true":"false");
}

void M62_PrivateWidget1::on_playback9_10IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback9_10", checked?"true":"false");
}

void M62_PrivateWidget1::on_playback9_10IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback9_10", checked?"true":"false");
}

void M62_PrivateWidget1::loadSettings()
{
    reset();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", "IN 1+2");
        WorkspaceObserver::setValue("DuckingSwitch", false);
        WorkspaceObserver::setValue("Threshold", getThresholdDefaultValue());
        WorkspaceObserver::setValue("Attack", getAttackDefaultValue());
        WorkspaceObserver::setValue("Reduction", getReductionDefaultValue());
        WorkspaceObserver::setValue("Release", getReleaseDefaultValue());
        WorkspaceObserver::setValue("IN1AUX", false);
        WorkspaceObserver::setValue("IN2AUX", false);
        WorkspaceObserver::setValue("IN1BT", false);
        WorkspaceObserver::setValue("IN2BT", false);
        WorkspaceObserver::setValue("IN1OTGIN", false);
        WorkspaceObserver::setValue("IN2OTGIN", false);
        WorkspaceObserver::setValue("IN1Playback1_2", false);
        WorkspaceObserver::setValue("IN2Playback1_2", false);
        WorkspaceObserver::setValue("IN1Playback3_4", false);
        WorkspaceObserver::setValue("IN2Playback3_4", false);
        WorkspaceObserver::setValue("IN1Playback5_6", false);
        WorkspaceObserver::setValue("IN2Playback5_6", false);
        WorkspaceObserver::setValue("IN1Playback7_8", false);
        WorkspaceObserver::setValue("IN2Playback7_8", false);
        WorkspaceObserver::setValue("IN1Playback9_10", false);
        WorkspaceObserver::setValue("IN2Playback9_10", false);
    }
    setINChannelName(WorkspaceObserver::value("ChannelName").toString());
    setButtonOFFChecked(WorkspaceObserver::value("DuckingSwitch").toBool(), false);
    setThresholdValue(WorkspaceObserver::value("Threshold").toDouble(), false);
    setAttackValue(WorkspaceObserver::value("Attack").toDouble(), false);
    setReductionValue(WorkspaceObserver::value("Reduction").toDouble(), false);
    setReleaseValue(WorkspaceObserver::value("Release").toDouble(), false);
    setIN1AUXChecked(WorkspaceObserver::value("IN1AUX").toBool(), false);
    setIN2AUXChecked(WorkspaceObserver::value("IN2AUX").toBool(), false);
    setIN1BTChecked(WorkspaceObserver::value("IN1BT").toBool(), false);
    setIN2BTChecked(WorkspaceObserver::value("IN2BT").toBool(), false);
    setIN1OTGChecked(WorkspaceObserver::value("IN1OTGIN").toBool(), false);
    setIN2OTGChecked(WorkspaceObserver::value("IN2OTGIN").toBool(), false);
    setplayback1_2IN1Checked(WorkspaceObserver::value("IN1Playback1_2").toBool(), false);
    setplayback1_2IN2Checked(WorkspaceObserver::value("IN2Playback1_2").toBool(), false);
    setplayback3_4IN1Checked(WorkspaceObserver::value("IN1Playback3_4").toBool(), false);
    setplayback3_4IN2Checked(WorkspaceObserver::value("IN2Playback3_4").toBool(), false);
    setplayback5_6IN1Checked(WorkspaceObserver::value("IN1Playback5_6").toBool(), false);
    setplayback5_6IN2Checked(WorkspaceObserver::value("IN2Playback5_6").toBool(), false);
    setplayback7_8IN1Checked(WorkspaceObserver::value("IN1Playback7_8").toBool(), false);
    setplayback7_8IN2Checked(WorkspaceObserver::value("IN2Playback7_8").toBool(), false);
    setplayback9_10IN1Checked(WorkspaceObserver::value("IN1Playback9_10").toBool(), false);
    setplayback9_10IN2Checked(WorkspaceObserver::value("IN2Playback9_10").toBool(), false);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Threshold", QString::number(WorkspaceObserver::value("Threshold").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Attack", QString::number(WorkspaceObserver::value("Attack").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Reduction", QString::number(WorkspaceObserver::value("Reduction").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Release", QString::number(WorkspaceObserver::value("Release").toDouble()));
        emit attributeChanged(this->objectName(), "Save_DuckingSwitch", QString::number(WorkspaceObserver::value("DuckingSwitch").toBool()));
        emit attributeChanged(this->objectName(), "Save_MapIN1", QString::number(calculateMapValue("IN1")));
        emit attributeChanged(this->objectName(), "Save_MapIN2", QString::number(calculateMapValue("IN2")));
    }
    updateAttribute();
}

void M62_PrivateWidget1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
            ui->DialThresholdT->setText("Threshold");
            ui->DialReductionT->setText("Reduction");
            ui->DialAttackT->setText("Attack");
            ui->DialReleaseT->setText("Release");
            ui->labelTitle_2->setText("Ducking Map");
            ui->labelIN1->setText("IN 1");
            ui->labelIN2->setText("IN 2");
            ui->labelAUX->setText("AUX");
            ui->labelBT->setText("BT");
            ui->labelOTG->setText("OTG");
            ui->labelpb12->setText("Playback 1/2");
            ui->labelpb34->setText("Playback 3/4");
            ui->labelpb56->setText("Playback 5/6");
            ui->labelpb78->setText("Playback 7/8");
            ui->labelpb910->setText("Playback 9/10");
        }
        else if(value == "Chinese")
        {
            ui->DialThresholdT->setText("闪避阈值");
            ui->DialReductionT->setText("闪避量");
            ui->DialAttackT->setText("启动时间");
            ui->DialReleaseT->setText("释放时间");
            ui->labelTitle_2->setText("闪避映射");
            ui->labelIN1->setText("IN 1");
            ui->labelIN2->setText("IN 2");
            ui->labelAUX->setText("AUX");
            ui->labelBT->setText("蓝牙");
            ui->labelOTG->setText("OTG");
            ui->labelpb12->setText("Playback 1/2");
            ui->labelpb34->setText("Playback 3/4");
            ui->labelpb56->setText("Playback 5/6");
            ui->labelpb78->setText("Playback 7/8");
            ui->labelpb910->setText("Playback 9/10");
        }
        setOFFButtonText(ui->buttonOFF->isChecked());
    }
}

void M62_PrivateWidget1::updateAttribute()
{
    if(mLineEditText != WorkspaceObserver::value("ChannelName").toString())
    {
        mLineEditText = WorkspaceObserver::value("ChannelName").toString();
    }
    if(mThreshold != WorkspaceObserver::value("Threshold").toDouble())
    {
        mThreshold = WorkspaceObserver::value("Threshold").toDouble();
        emit attributeChanged(this->objectName(), "Threshold", QString::number(mThreshold));
    }
    if(mAttack != WorkspaceObserver::value("Attack").toDouble())
    {
        mAttack = WorkspaceObserver::value("Attack").toDouble();
        emit attributeChanged(this->objectName(), "Attack", QString::number(mAttack));
    }
    if(mReduction != WorkspaceObserver::value("Reduction").toDouble())
    {
        mReduction = WorkspaceObserver::value("Reduction").toDouble();
        emit attributeChanged(this->objectName(), "Reduction", QString::number(mReduction));
    }
    if(mRelease != WorkspaceObserver::value("Release").toDouble())
    {
        mRelease = WorkspaceObserver::value("Release").toDouble();
        emit attributeChanged(this->objectName(), "Release", QString::number(mRelease));
    }

    uchar tempIn1 = calculateMapValue("IN1");
    uchar tempIn2 = calculateMapValue("IN2");
    bool isChanged1 = false;
    bool isChanged2 = false;
    if(mOFF != static_cast<int>(WorkspaceObserver::value("DuckingSwitch").toBool()))
    {
        isChanged1 = true;
        isChanged2 = true;
        mOFF = WorkspaceObserver::value("DuckingSwitch").toBool();
        emit attributeChanged(this->objectName(), "DuckingSwitch", QString::number(mOFF));
    }
    if(tempIn1 != mIn1)
    {
        isChanged1 = true;
        mIn1 = tempIn1;
        emit attributeChanged(this->objectName(), "MapIN1", QString::number(mIn1));
    }
    if(tempIn2 != mIn2)
    {
        isChanged2 = true;
        mIn2 = tempIn2;
        emit attributeChanged(this->objectName(), "MapIN2", QString::number(mIn2));
    }
    if(isChanged1){
        sendSigWorkState(1);
    }
    if(isChanged2){
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::saveAttribute(QString attribute, QString value)
{
    if(!value.isEmpty())
    {
        WorkspaceObserver::setValue(attribute, value);
    }

    auto saveValue = [this](const QString& attribute, const QString& value){
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), QString("Save_")+attribute, value);
        }
    };

    if(attribute == "Threshold")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Threshold").toDouble()));
    }
    else if(attribute == "Attack")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Attack").toDouble()));
    }
    else if(attribute == "Reduction")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Reduction").toDouble()));
    }
    else if(attribute == "Release")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Release").toDouble()));
    }
    else if(attribute == "DuckingSwitch")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("DuckingSwitch").toBool()));
    }
    else if(attribute.contains("IN1")){
        saveValue("MapIN1", QString::number(calculateMapValue("IN1")));
    }
    else if(attribute.contains("IN2")){
        saveValue("MapIN2", QString::number(calculateMapValue("IN2")));
    }
}

void M62_PrivateWidget1::reset()
{
    mThreshold = DBL_MAX;
    mAttack = DBL_MAX;
    mReduction = DBL_MAX;
    mRelease = DBL_MAX;
    mOFF = INT_MAX;
    mIN1AUX = INT_MAX;
    mIN2AUX = INT_MAX;
    mIN1BT = INT_MAX;
    mIN2BT = INT_MAX;
    mIN1OTGIN = INT_MAX;
    mIN2OTGIN = INT_MAX;
    mIN1Playback1_2 = INT_MAX;
    mIN2Playback1_2 = INT_MAX;
    mIN1Playback3_4 = INT_MAX;
    mIN2Playback3_4 = INT_MAX;
    mIN1Playback5_6 = INT_MAX;
    mIN2Playback5_6 = INT_MAX;
    mIN1Playback7_8 = INT_MAX;
    mIN2Playback7_8 = INT_MAX;
    mIN1Playback9_10 = INT_MAX;
    mIN2Playback9_10 = INT_MAX;
    mIn1 = INT_MAX;
    mIn2 = INT_MAX;
}

uchar M62_PrivateWidget1::calculateMapValue(const QString& prefix)
{
    uchar result = 0;
    result = (result & ~(1 << 0)) | ((uchar)WorkspaceObserver::value(prefix + "AUX").toBool() << 0);
    result = (result & ~(1 << 1)) | ((uchar)WorkspaceObserver::value(prefix + "BT").toBool() << 1);
    result = (result & ~(1 << 2)) | ((uchar)WorkspaceObserver::value(prefix + "OTGIN").toBool() << 2);
    result = (result & ~(1 << 3)) | ((uchar)WorkspaceObserver::value(prefix + "Playback1_2").toBool() << 3);
    result = (result & ~(1 << 4)) | ((uchar)WorkspaceObserver::value(prefix + "Playback3_4").toBool() << 4);
    result = (result & ~(1 << 5)) | ((uchar)WorkspaceObserver::value(prefix + "Playback5_6").toBool() << 5);
    result = (result & ~(1 << 6)) | ((uchar)WorkspaceObserver::value(prefix + "Playback7_8").toBool() << 6);
    result = (result & ~(1 << 7)) | ((uchar)WorkspaceObserver::value(prefix + "Playback9_10").toBool() << 7);
    return result;
}

void M62_PrivateWidget1::setOFFButtonText(bool isChecked){
    auto language = QSettings().value("Language").toString();
    if(language == "English")
        ui->buttonOFF->setText(isChecked ? "ON" : "OFF");
    else if(language == "Chinese")
        ui->buttonOFF->setText(ui->buttonOFF->isChecked() ? "开" : "关");
}

void M62_PrivateWidget1::sendSigWorkState(int index)
{
    emit attributeChanged(this->objectName(), index==1?"IN1WorkState":"IN2WorkState", mOFF==1?(index==1?(mIn1==0?"0":"1"):(mIn2==0?"0":"1")):"0");
}