{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.19"}, "projectIndex": 0, "source": "."}, {"build": "Source", "hasInstallRule": true, "jsonFile": "directory-Source-Release-834bdb0014681ba8dd01.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "Source", "targetIndexes": [0, 1, 2, 3]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "TPCC", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 1, "id": "TPCC::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC-Release-c0f42b3d9aaa36ab6362.json", "name": "TPCC", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_autogen::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_autogen-Release-02c82c88af6829094dfa.json", "name": "TPCC_autogen", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_autogen_timestamp_deps::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_autogen_timestamp_deps-Release-957a5ee5098b238bfe67.json", "name": "TPCC_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_ui_property_check-Release-4c0b806121bf3f3f5163.json", "name": "TPCC_ui_property_check", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build", "source": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter"}, "version": {"major": 2, "minor": 7}}