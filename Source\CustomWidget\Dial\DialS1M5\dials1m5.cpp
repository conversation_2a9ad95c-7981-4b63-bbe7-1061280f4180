#include "dials1m5.h"
#include <qnamespace.h>
#include "globalfont.h"
#include <QPainter>
#include <QImage>
#include <QPixmap>
#include <QPainterPath>
#include <QSvgRenderer>

DialS1M5::DialS1M5(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M5::~DialS1M5()
{

}

// override
void DialS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mRectDial.setY(rect().y() + (rect().height() - diameter)/2);
    mRectDial.setX(rect().x() + (rect().width() - diameter)/2);
    mRectDial.setWidth(diameter);
    mRectDial.setHeight(diameter);
}
void DialS1M5::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M5::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M5::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M5::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * mValueStep;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M5::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M5::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    value += e->angleDelta().y() / 120 * mValueStepWheel;
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M5::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        value += 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        value -= 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
QPixmap DialS1M5::extractArcPartSquare(const QPixmap &source, qreal mCurValueAngle, bool mDoublePercent)
{
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int wh = qMin(source.width(), source.height());
    int logicalWh = wh / devicePixelRatio;

    QPixmap result(wh, wh);
    result.setDevicePixelRatio(devicePixelRatio);
    result.fill(Qt::transparent);

    QPainter painter(&result);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    QImage mask(wh, wh, QImage::Format_ARGB32_Premultiplied);
    mask.setDevicePixelRatio(devicePixelRatio);
    mask.fill(Qt::transparent);
    {
        QPainter maskPainter(&mask);
        maskPainter.setRenderHint(QPainter::Antialiasing, true);
        maskPainter.setRenderHint(QPainter::SmoothPixmapTransform, true);
        maskPainter.setPen(Qt::NoPen);
        maskPainter.setBrush(Qt::white);
        QRectF rect(0, 0, logicalWh, logicalWh);
        qreal startDeg = mDoublePercent ? 90 : (225 - mCurValueAngle);
        qreal spanDeg = mDoublePercent ? (135 - mCurValueAngle) : mCurValueAngle;
        QPainterPath path;
        path.moveTo(rect.center());
        path.arcTo(rect, startDeg, spanDeg);
        path.closeSubpath();
        maskPainter.drawPath(path);
    }

    painter.drawPixmap(QRect(0, 0, logicalWh, logicalWh), source);
    painter.setCompositionMode(QPainter::CompositionMode_DestinationIn);
    painter.drawImage(QRect(0, 0, logicalWh, logicalWh), mask);

    painter.end();
    return result;
}
void DialS1M5::drawBG(QPainter* painter)
{
    painter->fillRect(rect(), Qt::transparent);
}
void DialS1M5::drawElement(QPainter* painter)
{
    painter->save();
    QPen pen=painter->pen();
    qreal innerSpace = mRectDial.width() * 0.125;
    QRectF innerRect = mRectDial.adjusted(innerSpace, innerSpace, -innerSpace, -innerSpace);
    qreal mCurValueAngle = 270 * (mValue - mValueMin) / (mValueMax - mValueMin);
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int pixelDiameter = mRectDial.width() * devicePixelRatio;

    QPixmap pixmapBackground(":/Icon/EllipseBackground.png");
    pixmapBackground = pixmapBackground.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmapBackground.setDevicePixelRatio(devicePixelRatio);
    painter->drawPixmap(mRectDial, pixmapBackground);

    QPixmap pixmap(":/Icon/Ellipse.png");
    pixmap = pixmap.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmap.setDevicePixelRatio(devicePixelRatio);
    pixmap = extractArcPartSquare(pixmap, mCurValueAngle, mDoublePercent);
    painter->drawPixmap(mRectDial, pixmap);

    if(mValueShowArrow)
    {
        painter->save();
        int penWidth = mRectDial.width() * 0.08;
        qreal innerCircleRadius = mRectDial.width()/2.0 - penWidth * 2.0;
        qreal indicatorWidth = innerCircleRadius * 0.05;
        qreal indicatorHeight = innerCircleRadius *0.23;
        painter->translate(innerRect.center());
        qreal degRotate = -135 + mCurValueAngle;
        painter->rotate(degRotate);
        QRectF indicatorRect(-indicatorWidth/2.0, -innerRect.width()*0.46, indicatorWidth, indicatorHeight);
        qreal cornerRadius = indicatorWidth/4.0;
        painter->setBrush(mColorHandle);
        pen.setColor(mColorHandle);
        pen.setWidth(penWidth * 0.4);
        painter->setPen(pen);
        painter->drawRoundedRect(indicatorRect, cornerRadius, cornerRadius);
        painter->restore();
    }

    if(mValueShowText)
    {
        painter->resetTransform();
        int widthText=mRectDial.width() * 0.6;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) *0.48, widthText, heightText);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+88", rectText));
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValueShowSign)
        {
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', mPrecision);
            }
            else
            {
                str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}

// setter & getter
DialS1M5& DialS1M5::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M5& DialS1M5::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M5& DialS1M5::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M5& DialS1M5::setRange(float min, float max)
{
    if(min > max)
    {
        return *this;
    }
    mValueMin = min;
    mValueMax = max;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M5& DialS1M5::setStep(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStep = step;
    return *this;
}
DialS1M5& DialS1M5::setStepWheel(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStepWheel = step;
    return *this;
}
DialS1M5& DialS1M5::setPrecision(int precision)
{
    if(precision > 3 || precision < 0)
    {
        return *this;
    }
    mPrecision = precision;
    update();
    return *this;
}
DialS1M5& DialS1M5::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M5& DialS1M5::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M5& DialS1M5::setDoublePercent(bool status)
{
    mDoublePercent = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showSign(bool status)
{
    mValueShowSign = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M5& DialS1M5::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}
