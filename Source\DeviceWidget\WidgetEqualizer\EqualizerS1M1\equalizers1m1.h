#ifndef EQUALIZERS1M1_H
#define EQUALIZERS1M1_H

#include <qfont.h>
#include "equalizerbase.h"

namespace Ui {
class EqualizerS1M1;
}

class EqualizerS1M1 : public EqualizerBase
{
public:
    explicit EqualizerS1M1(QWidget *parent = nullptr);
    ~EqualizerS1M1();
    EqualizerS1M1& setName(const QString& name);
    EqualizerS1M1& setFont(const QFont& font);
    void setEqualizerBands(int count);
    void setEqualizerData(const QString& index, const QString& type, const QString& data);
    void setScaleFactor(double sizeFactor);

    void setStateTarget(bool state);
    void setStateSource(bool state);
    void setStateEachFilter(bool state);
    void setStateCombined(bool state);
    void setStateFiltered(bool state);
    void setFilteredType(int type);
    void setTargetFile(QString path);
    void setSourceFile(QString path);

    void setSwitchStatus(bool is);
    void setGainInputLeft(float value);
    void setGainInputRight(float value);
    void setGainOutputLeft(float value);
    void setGainOutputRight(float value);
    void setVolumeMeterInputLeft(int value);
    void setVolumeMeterInputRight(int value);
    void setVolumeMeterOutputLeft(int value);
    void setVolumeMeterOutputRight(int value);

protected:
    void setSizeFactor(double sizeFactor);
    void showEvent(QShowEvent* e);

private:
    Ui::EqualizerS1M1 *ui;
    float mScaleRatio=1.0;
    QWidget* mWidget=nullptr;
    QFont mFont;
    QHash<QObject*,QHash<QString,QString>> mHashLanguages;;
};

#endif // EQUALIZERS1M1_H
